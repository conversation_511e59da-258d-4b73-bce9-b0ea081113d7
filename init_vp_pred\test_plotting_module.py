"""
Test script for the new plotting module

This script verifies that the plotting module works correctly
"""
import sys
import os
import json
import numpy as np
from pathlib import Path

# Add the current directory to the path so we can import plot_vp_improved
sys.path.append(str(Path(__file__).parent))

import plot_vp_improved as plot_vp


def test_create_training_plots():
    """Test the create_training_plots function"""
    print("Testing create_training_plots...")
    
    # Create sample training history
    history = {
        'train_loss': [0.1, 0.08, 0.06, 0.05, 0.04],
        'val_loss': [0.12, 0.09, 0.07, 0.06, 0.05],
        'val_r2': [0.7, 0.75, 0.8, 0.82, 0.85],
        'val_rmse': [20.0, 18.0, 16.0, 15.0, 14.0]
    }
    
    # Create output directory
    output_dir = Path("test_outputs/training")
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Test creating plots
    try:
        plot_file = plot_vp.create_training_plots(history, output_dir)
        print(f"✅ Training plots created successfully: {plot_file}")
        return True
    except Exception as e:
        print(f"❌ Error creating training plots: {e}")
        return False


def test_create_cv_plots():
    """Test the create_cv_plots function"""
    print("Testing create_cv_plots...")
    
    # Create sample CV results
    cv_results = [
        {'r2': 0.8, 'rmse': 15.0, 'mae': 12.0, 'mse': 225.0},
        {'r2': 0.75, 'rmse': 16.0, 'mae': 13.0, 'mse': 256.0},
        {'r2': 0.82, 'rmse': 14.5, 'mae': 11.5, 'mse': 210.0},
        {'r2': 0.78, 'rmse': 15.5, 'mae': 12.5, 'mse': 240.0},
        {'r2': 0.81, 'rmse': 14.8, 'mae': 12.2, 'mse': 220.0}
    ]
    
    cv_summary = {
        'r2': {
            'mean': 0.792,
            'std': 0.025,
            'min': 0.75,
            'max': 0.82,
            'values': [0.8, 0.75, 0.82, 0.78, 0.81]
        },
        'rmse': {
            'mean': 15.16,
            'std': 0.61,
            'min': 14.5,
            'max': 16.0,
            'values': [15.0, 16.0, 14.5, 15.5, 14.8]
        }
    }
    
    # Create output directory
    output_dir = Path("test_outputs/validation")
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Test creating plots
    try:
        plot_file = plot_vp.create_cv_plots(cv_results, cv_summary, output_dir)
        print(f"✅ CV plots created successfully: {plot_file}")
        return True
    except Exception as e:
        print(f"❌ Error creating CV plots: {e}")
        return False


def test_visualize_prediction_results():
    """Test the visualize_prediction_results function"""
    print("Testing visualize_prediction_results...")
    
    # Create sample prediction results
    predictions = np.array([100, 105, 110, 115, 120, 125, 130, 135, 140, 145])
    actuals = np.array([102, 107, 108, 117, 12, 123, 132, 133, 142, 143])
    
    prediction_results = {
        'predictions': predictions,
        'actuals': actuals,
        'metrics': {
            'r2': 0.95,
            'rmse': 2.5,
            'mae': 2.0,
            'mse': 6.25
        }
    }
    
    # Create output directory
    output_dir = Path("test_outputs/prediction")
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Test creating plots
    try:
        plot_file = plot_vp.visualize_prediction_results(prediction_results, output_dir)
        print(f"✅ Prediction plots created successfully: {plot_file}")
        return True
    except Exception as e:
        print(f"❌ Error creating prediction plots: {e}")
        return False


def test_load_and_visualize_functions():
    """Test the load and visualize functions"""
    print("Testing load and visualize functions...")
    
    # Create sample data files
    test_dir = Path("test_outputs")
    
    # Create sample training history file
    training_dir = test_dir / "training"
    training_dir.mkdir(parents=True, exist_ok=True)
    history = {
        'train_loss': [0.1, 0.08, 0.06, 0.05, 0.04],
        'val_loss': [0.12, 0.09, 0.07, 0.06, 0.05],
        'val_r2': [0.7, 0.75, 0.8, 0.82, 0.85],
        'val_rmse': [20.0, 18.0, 16.0, 15.0, 14.0]
    }
    with open(training_dir / "training_history.json", "w") as f:
        json.dump(history, f)
    
    # Create sample CV results file
    validation_dir = test_dir / "validation"
    validation_dir.mkdir(parents=True, exist_ok=True)
    cv_data = {
        'cv_results': [
            {'r2': 0.8, 'rmse': 15.0, 'mae': 12.0, 'mse': 225.0},
            {'r2': 0.75, 'rmse': 16.0, 'mae': 13.0, 'mse': 256.0},
            {'r2': 0.82, 'rmse': 14.5, 'mae': 11.5, 'mse': 210.0},
            {'r2': 0.78, 'rmse': 15.5, 'mae': 12.5, 'mse': 240.0},
            {'r2': 0.81, 'rmse': 14.8, 'mae': 12.2, 'mse': 220.0}
        ],
        'cv_summary': {
            'r2': {
                'mean': 0.792,
                'std': 0.025,
                'min': 0.75,
                'max': 0.82,
                'values': [0.8, 0.75, 0.82, 0.78, 0.81]
            }
        }
    }
    with open(validation_dir / "cv_results.json", "w") as f:
        json.dump(cv_data, f)
    
    # Create sample prediction results file
    prediction_dir = test_dir / "prediction"
    prediction_dir.mkdir(parents=True, exist_ok=True)
    pred_data = {
        'predictions': [100, 105, 110, 115, 120, 125, 130, 135, 140, 145],
        'actuals': [102, 107, 108, 117, 122, 123, 132, 133, 142, 143],
        'metrics': {
            'r2': 0.95,
            'rmse': 2.5,
            'mae': 2.0,
            'mse': 6.25
        }
    }
    with open(prediction_dir / "detailed_predictions.json", "w") as f:
        json.dump(pred_data, f)
    
    # Test loading and visualizing
    try:
        # Test loading and visualizing training results
        plot_file = plot_vp.load_and_visualize_training_results(str(training_dir))
        print(f"✅ Training results loaded and visualized: {plot_file}")
        
        # Test loading and visualizing CV results
        plot_file = plot_vp.load_and_visualize_cv_results(str(validation_dir))
        print(f"✅ CV results loaded and visualized: {plot_file}")
        
        # Test loading and visualizing prediction results
        plot_file = plot_vp.load_and_visualize_prediction_results(str(prediction_dir))
        print(f"✅ Prediction results loaded and visualized: {plot_file}")
        
        return True
    except Exception as e:
        print(f"❌ Error loading and visualizing results: {e}")
        return False


def main():
    """Main test function"""
    print("🧪 Testing plotting module...")
    print("=" * 50)
    
    # Run all tests
    tests = [
        test_create_training_plots,
        test_create_cv_plots,
        test_visualize_prediction_results,
        test_load_and_visualize_functions
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {e}")
            results.append(False)
        print()
    
    # Print summary
    print("=" * 50)
    print("📊 Test Summary:")
    passed = sum(results)
    total = len(results)
    print(f"✅ Passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 All tests passed!")
        return 0
    else:
        print("❌ Some tests failed!")
        return 1


if __name__ == "__main__":
    sys.exit(main())