# VP Prediction Codebase Structure

## 📋 Overview

This document provides a comprehensive mapping of the VP (Acoustic Velocity) Prediction codebase structure. The project implements a transformer-based machine learning pipeline for predicting acoustic velocity from well log data, featuring both standalone and modular package architectures.

## 🏗️ High-Level Architecture

```
init_vp_pred/                          # Root project directory
├── 🚀 MAIN COMPONENTS                  # Core standalone scripts
├── 📦 vp_predictor/                    # Modular package architecture
├── 📊 vp_prediction_outputs/           # Auto-generated results
├── 🗂️ archives/                        # Cleaned/archived files and tidy-up results
├── 📄 DATA FILES                      # Sample and test data
├── 📝 DOCUMENTATION                   # Guides and setup docs
└── ⚙️ PROJECT CONFIGURATION           # Setup and requirements
```

## 🗂️ Detailed Directory Structure

### 🚀 Root Level - Main Components

```
.
├── train_vp_improved.py               # 🎯 MAIN ENTRY POINT - Complete pipeline
├── plot_vp_improved.py                # 📊 Visualization functions
├── visualize_vp_results.py            # 📊 Standalone visualization script (moved to archives/)
├── test_vp_pipeline.py                # 🧪 Comprehensive test suite
├── test_plotting_module.py            # 🧪 Plotting module test suite
├── dataset.py                         # Basic dataset utilities
├── utils.py                           # Utility functions (device, checkpoints, metrics)
└── __init__.py                        # Module initialization
```

#### Key Scripts Description:

| File | Purpose | Key Classes/Functions | Pipeline Role |
|------|---------|----------------------|---------------|
| **[`train_vp_improved.py`](train_vp_improved.py)** | 🎯 **MAIN PIPELINE ORCHESTRATOR** | `VpPredictionPipeline`, `VpPipelineConfig`, `main()` | **PRIMARY ENTRY POINT** - Full 3-stage workflow |
| **[`plot_vp_improved.py`](plot_vp_improved.py)** | 📊 Visualization functions | `create_training_plots()`, `create_cv_plots()`, `visualize_prediction_results()` | **SUPPORTING** - Plotting utilities |
| **[`visualize_vp_results.py`](archives/visualize_vp_results.py)** | 📊 Standalone visualization | `main()` | **UTILITY** - Visualization without retraining (archived) |
| **[`test_vp_pipeline.py`](test_vp_pipeline.py)** | Testing framework | `run_comprehensive_test()` | **TESTING** - Main validation suite |
| **[`test_plotting_module.py`](test_plotting_module.py)** | Plotting test framework | `main()` | **TESTING** - Plotting validation suite |
| **[`dataset.py`](dataset.py)** | Basic dataset utilities | `VpDataset` | **SUPPORTING** - Data handling |
| **[`utils.py`](utils.py)** | Core utilities | `get_device()`, `save_checkpoint()`, `EarlyStopping` | **SUPPORTING** - Utility functions |

### 📦 vp_predictor/ - Modular Package Architecture

```
vp_predictor/                          # Advanced modular package
├── __init__.py                        # Package entry point & backward compatibility
├── vp_model_improved.py               # VP-specific implementations
├── model.py                           # Base model components
├── utils.py                           # Package utilities
├── las_processor.py                   # Data processing
├── predictor.py                       # High-level prediction API
│
├── 🧠 core/                           # Core architecture components
│   ├── __init__.py                    # Core exports
│   ├── transformer.py                 # GeneralWellLogTransformer
│   ├── decoder.py                     # Specialized decoders
│   ├── normalizer.py                  # Data normalization
│   ├── dataset.py                     # Advanced dataset handling
│   ├── loss_functions.py              # Loss function library
│   └── training.py                    # Training management
│
├── ⚙️ configs/                        # Configuration system
│   ├── __init__.py                    # Config exports
│   ├── curves.py                      # Curve configurations
│   ├── models.py                      # Model templates
│   ├── training.py                    # Training configurations
│   └── validation.py                  # Validation utilities
│
└── 🔌 api/                            # API interfaces
    ├── __init__.py                    # API exports
    ├── predictor.py                   # General prediction API
    └── legacy.py                      # Backward compatibility
```

#### Package Main Entry Points:

| File | Purpose | Key Classes/Functions | Pipeline Role |
|------|---------|----------------------|---------------|
| **[`vp_predictor/vp_model_improved.py`](vp_predictor/vp_model_improved.py)** | VP model architectures & standalone training | `MWLT_Vp_Small/Base/Large`, `VpDataNormalizer`, `train_improved_vp_model()` | **SECONDARY** - Component library + simple training |
| **[`vp_predictor/model.py`](vp_predictor/model.py)** | Base transformer components | `MWL_Transformer`, `TransformerBlock`, `Input_Embedding`, `claculate_flop_param()` | **SUPPORTING** - Core architecture + performance analysis |
| **[`vp_predictor/las_processor.py`](vp_predictor/las_processor.py)** | LAS file processing | `LASProcessor`, `create_test_las_file()` | **SUPPORTING** - Data processing + test utilities |
| **[`vp_predictor/predictor.py`](vp_predictor/predictor.py)** | High-level prediction API | `VpPredictor`, `VpTransformerAPI` | **API** - Integration wrapper |
| **[`vp_predictor/api/predictor.py`](vp_predictor/api/predictor.py)** | General prediction API | `GeneralWellLogPredictor` | **API** - Modern general-purpose interface |

#### Package Architecture Details:

| Module | Purpose | Key Components |
|--------|---------|----------------|
| **core/** | Core ML architecture | `GeneralWellLogTransformer`, `GeneralDecoder`, `GeneralDataNormalizer` |
| **configs/** | Configuration management | `MODEL_TEMPLATES`, `CURVE_CONFIGURATIONS`, `TRAINING_TEMPLATES` |
| **api/** | User interfaces | `GeneralWellLogPredictor`, `VpPredictor` (legacy) |

### 🎯 Console Commands (Package Installation)

When installed as a package via `pip install -e .`, the following console commands become available:

| Command | Purpose | Equivalent Script |
|---------|---------|-------------------|
| **`vp-train`** | Run VP training pipeline | `python train_vp_improved.py` |
| **`vp-test`** | Run comprehensive test suite | `python test_vp_pipeline.py` |

### 📊 vp_prediction_outputs/ - Results Directory

```
vp_prediction_outputs/                 # Auto-generated results (git-ignored)
├── training/                          # Training stage outputs
│   ├── best_vp_model.pth              # Trained model checkpoint
│   ├── training_history.json          # Training metrics & loss curves
│   ├── training_results.json          # Final training results
│   ├── training_plots/                # Visualization plots
│   └── checkpoints/                   # Intermediate checkpoints
│
├── validation/                        # Validation stage outputs
│   ├── cv_results.json                # Cross-validation results
│   ├── validation_report.json         # Performance analysis
│   ├── validation_plots/              # Validation visualizations
│   └── metrics_summary.json           # Aggregated metrics
│
├── prediction/                        # Prediction stage outputs
│   ├── detailed_predictions.json      # Per-sample predictions
│   ├── prediction_report.json         # Prediction analysis
│   ├── prediction_plots/              # Result visualizations
│   └── performance_metrics.json       # Test set performance
│
├── pipeline_config.json               # Used configuration
├── pipeline_summary.json              # Overall pipeline results
└── pipeline.log                       # Execution log
```

### 🗂️ archives/ - Cleanup and Archive Directory

```
archives/                              # Repository cleanup and archived files
├── cleaned_files/                     # Files removed during cleanup phases
│   ├── 20250906-1324_phase1/          # Phase 1 cleanup (empty)
│   ├── 20250906-1324_phase2/          # Phase 2 cleanup (empty)
│   ├── 20250906-1326_phase1/          # Phase 1 cleanup with test outputs
│   │   ├── cache_files/               # Cached data files
│   │   └── test_outputs/              # Test execution results
│   │       ├── prediction/            # Prediction test results
│   │       │   ├── detailed_predictions.json
│   │       │   └── prediction_results.png
│   │       ├── training/              # Training test results
│   │       │   ├── training_history.json
│   │       │   └── training_progress.png
│   │       └── validation/            # Validation test results
│   │           └── cv_results.json
│   └── 20250906-1417_phase2/          # Phase 2 cleanup with modules
│       ├── dataset.py                 # Archived dataset utilities
│       └── utils.py                   # Archived utility functions
├── metadata/                          # Cleanup tracking and metadata
│   ├── pre_cleanup_inventory.csv      # Inventory before cleanup
│   └── protected_hdf5_files.csv       # Protected HDF5 files list
└── visualize_vp_results.py            # Archived visualization script
```

**Archive Directory Purpose**:
- 🗂️ **Cleanup History**: Timestamped records of cleanup phases
- 📋 **Metadata Tracking**: CSV files tracking what was cleaned and protected
- 🛡️ **Recovery Support**: Archived files can be restored if needed
- 📊 **Test Results Archive**: Historical test outputs from cleanup phases
- 🧹 **Tidy-up Results**: Contains files and folders that were cleaned up during repository organization

**Archive Structure Details**:

| Directory | Purpose | Contents |
|-----------|---------|----------|
| **cleaned_files/** | Files removed during cleanup | Timestamped cleanup phases with original file structures |
| **metadata/** | Cleanup tracking data | CSV files with inventories and protection lists |
| **Root level** | Individual archived files | Standalone files moved during cleanup (e.g., old visualization scripts) |

**Cleanup Phases**:
- **Phase 1 (1324 & 1326)**: Initial cleanup with test output preservation
- **Phase 2 (1417)**: Module consolidation and utility cleanup
```

### 📄 Data Files

```
├── A1.hdf5                            # Primary training dataset
├── A2.hdf5                            # Secondary training dataset
├── A1_converted.las                   # Sample LAS data file 1
├── test_well.las                      # Sample LAS data file 2
└── __pycache__/                       # Python cache (git-ignored)
```

### 📝 Documentation

```
├── README.md                          # 📋 Main project overview
├── VP_QUICK_START_GUIDE.md            # 🚀 Step-by-step usage guide
├── SETUP_CHECKLIST.md                 # ✅ Installation checklist
├── 1_List_of_cleaned.md               # 📋 Cleanup tracking document
├── CLEANUP_COMPLETION_REPORT.md       # 📊 Final cleanup report
├── POST_CLEANUP_INVENTORY.txt         # 📄 Post-cleanup inventory
└── docs/                              # Documentation folder
    └── plan/
        └── 1a_plan_refactoring_vp_ph1.md  # 🔧 Refactoring plan
```

### ⚙️ Project Configuration

```
├── requirements.txt                   # 📋 Python dependencies
├── setup.py                          # 📦 Package installation script
└── Codebase.md                        # 📖 This documentation
```

## 🔄 Data Flow Architecture

### Pipeline Execution Flow:

```
🎯 train_vp_improved.py (main entry)
       ↓
📊 VpDataProcessor.find_data_files()
       ↓
📦 VpDataset (data preparation)
       ↓
🧠 MWLT_Vp_Base/Small/Large (from vp_predictor package)
       ↓
🔧 VpTrainingManager (training)
       ↓
✅ VpValidationManager (validation)
       ↓
🎯 VpPredictionManager (prediction)
       ↓
📊 Results saved to vp_prediction_outputs/
```

### Component Dependencies:

```
train_vp_improved.py (ROOT LEVEL - MAIN PIPELINE)
├── plot_vp_improved.py              # Visualization functions
├── utils.py                          # Core utilities
├── dataset.py                        # Basic dataset utilities
└── vp_predictor/ (PACKAGE)           # Enhanced package features
    ├── vp_model_improved.py          # Model architectures
    ├── model.py                      # Base components
    ├── utils.py                      # Package utilities
    ├── las_processor.py              # Data processing
    ├── predictor.py                  # High-level prediction API
    ├── core/                         # Advanced architectures
    ├── configs/                      # Configuration system
    └── api/                          # High-level APIs
```

### 🎯 **MAIN PIPELINE ENTRY POINTS**

| Priority | Script | Location | Purpose |
|----------|--------|----------|---------|
| **1st** | `train_vp_improved.py` | Root | **PRIMARY** - Complete 3-stage pipeline |
| **2nd** | `plot_vp_improved.py` | Root | **UTILITY** - Visualization functions |
| **3rd** | `test_vp_pipeline.py` | Root | **TESTING** - Pipeline validation |
| **4th** | `test_plotting_module.py` | Root | **TESTING** - Plotting validation |
| **5th** | `vp_predictor/las_processor.py` | Package | **UTILITY** - Data processing tools |
| **6th** | `vp_predictor/model.py` | Package | **UTILITY** - Performance analysis |

## 🧩 Key Components

### 🎯 Main Pipeline (`train_vp_improved.py`) - PRIMARY CODEBASE

**Purpose**: 🎯 **MAIN ENTRY POINT** - Complete end-to-end VP prediction pipeline with professional workflow management

**Key Classes**:
- `VpPredictionPipeline`: **Main orchestrator class** - Manages complete 3-stage workflow
- `VpPipelineConfig`: Configuration management with JSON support
- `VpDataProcessor`: Data loading and preprocessing
- `VpTrainingManager`: Model training coordination
- `VpValidationManager`: Cross-validation and performance assessment
- `VpPredictionManager`: Inference and result generation

**Key Features**:
- ✅ **Three-stage workflow**: Training → Validation → Prediction
- ✅ **Professional logging** and progress tracking
- ✅ **Organized output structure** with JSON reports
- ✅ **Command-line interface** with multiple execution modes
- ✅ **Error handling** and comprehensive result analysis
- ✅ **Integration** with vp_predictor package components

**Usage**:
```bash
# Complete pipeline (RECOMMENDED)
python train_vp_improved.py

# Individual stages
python train_vp_improved.py --stage training
python train_vp_improved.py --stage validation --model-path path/to/model.pth
python train_vp_improved.py --stage prediction --model-path path/to/model.pth
```

### 📚 Model Library (`vp_predictor/vp_model_improved.py`) - COMPONENT LIBRARY

**Purpose**: VP-specific model architectures and standalone training functions (simpler alternative)

**Location**: Available in `vp_predictor/` package (not in root directory)

**Key Components**:
- `MWLT_Vp_Small/Base/Large`: Model architecture variants
- `VpDataNormalizer`: VP-specific data normalization
- `VpLoss`: Custom loss function for VP prediction
- `train_improved_vp_model()`: Simple training function
- `evaluate_vp_model()`: Model evaluation
- `predict_vp_from_hdf5()`: Direct prediction from HDF5

**Key Features**:
- ✅ **Model definitions** and configurations
- ✅ **Standalone training** (basic implementation)
- ✅ **Direct inference** capabilities
- ✅ **Package-based architecture** (imported by main pipeline)

**Usage**:
```python
# Import from package
from vp_predictor.vp_model_improved import MWLT_Vp_Base, VpDataNormalizer

# Used by train_vp_improved.py pipeline
# No direct command-line interface (use train_vp_improved.py instead)
```

---

## 🎆 **PIPELINE COMPARISON**

| Aspect | [`train_vp_improved.py`](train_vp_improved.py) | [`vp_predictor/vp_model_improved.py`](vp_predictor/vp_model_improved.py) |
|--------|----------------------------------------|----------------------------------------|
| **Purpose** | 🎯 **MAIN PIPELINE** | 📚 **COMPONENT LIBRARY** |
| **Location** | **Root directory** | **Package directory** |
| **Complexity** | **Professional 3-stage workflow** | **Simple training functions** |
| **Output Organization** | **Structured directories + reports** | **Basic file output** |
| **Validation** | **Cross-validation + metrics** | **Basic train/val split** |
| **Configuration** | **JSON-based configuration system** | **Simple config dictionaries** |
| **Logging** | **Professional logging + progress tracking** | **Basic print statements** |
| **Usage** | **Production-ready pipeline** | **Development/experimentation** |
| **Integration** | **vp_predictor package integration** | **Package component** |
| **Command Line** | **✅ Full CLI interface** | **❌ No direct CLI (package only)** |
| **Recommended For** | **✅ Main usage, Complete workflows** | **🔧 Development, Quick tests** |

## 🛠️ **WHICH ONE TO USE?**

### 🎯 **Use `train_vp_improved.py` when** (RECOMMENDED):
- ✅ Running complete VP prediction workflows
- ✅ Need professional-grade results and reports
- ✅ Want organized output structure
- ✅ Require comprehensive validation
- ✅ Production or research use

### 🔧 **Use `vp_predictor/vp_model_improved.py` when**:
- 📚 Working on model development
- 📚 Quick training experiments
- 📚 Need standalone model definitions
- 📚 Simple inference tasks
- 📚 Importing components into other scripts

### 🧬 Base Architecture (`vp_predictor/model.py`)

**Purpose**: Foundational transformer components shared by all models

**Location**: Available in `vp_predictor/` package

**Base Components**:
- `MWL_Transformer`: Base transformer architecture
- `TransformerBlock`: Multi-head attention + feed-forward
- `Input_Embedding`: Well log data embedding
- `PositionalEncoding`: Sequence position encoding
- `ResCNN`: Residual CNN blocks
- `MWLT_Small/Base/Large`: Base model variants

**Executable Features**:
- `claculate_flop_param()`: Performance analysis function
- **Command-line execution**: `python vp_predictor/model.py` (calculates model parameters and FLOPs)

**Data Handling**:
- `VpDataset`: PyTorch dataset implementation (both files)
- Input normalization and sequence processing
- Support for data augmentation and quality filtering

### ⚙️ Configuration System (`vp_predictor/configs/`)

**Templates Available**:
- Model templates: `vp_prediction`, `multi_curve_basic`, etc.
- Training templates: Different optimization strategies
- Curve configurations: Input/output curve definitions

**Key Files**:
- `models.py`: Model architecture templates
- `curves.py`: Well log curve configurations
- `training.py`: Training parameter templates
- `validation.py`: Validation configurations

### 🔌 API Interfaces (`vp_predictor/api/`)

**High-Level APIs**:
- `GeneralWellLogPredictor`: Modern general-purpose API
- `VpPredictor`: Backward-compatible VP-specific API
- `VpTransformerAPI`: Legacy transformer interface

### 🛠️ Utility Scripts

**Additional Main Components**:

| File | Purpose | Key Functions | Pipeline Role |
|------|---------|---------------|---------------|
| **[`vp_predictor/las_processor.py`](vp_predictor/las_processor.py)** | LAS file processing & testing | `LASProcessor`, `create_test_las_file()` | **UTILITY** - Data processing + test data generation |
| **[`test_plotting_module.py`](test_plotting_module.py)** | Plotting module testing | `main()`, test functions | **TESTING** - Visualization validation |

**Executable Utilities**:
```bash
# Generate test LAS files and process HDF5 data
python vp_predictor/las_processor.py

# Test plotting functionality
python test_plotting_module.py

# Calculate model performance metrics
python vp_predictor/model.py
```

## 📊 Model Performance Expectations

| Metric | Training | Validation | Test |
|--------|----------|------------|------|
| **R²** | 0.75 - 0.90 | 0.70 - 0.85 | 0.65 - 0.80 |
| **RMSE** | Low | Moderate | Acceptable |
| **MAE** | Low | Moderate | Acceptable |

**Training Time**: 10-30 minutes (typical)

## 🔧 Installation & Setup

### Quick Setup:
```bash
# Clone repository
cd init_vp_pred

# Install dependencies
pip install -r requirements.txt

# Run tests
python test_vp_pipeline.py

# Execute pipeline
python train_vp_improved.py
```

### Package Installation:
```bash
# Install as package
pip install -e .

# Use console commands
vp-train    # Equivalent to: python train_vp_improved.py
vp-test     # Equivalent to: python test_vp_pipeline.py
```

## 🧪 Testing Framework

**Main Test Scripts**:

| Script | Purpose | Usage |
|--------|---------|-------|
| **[`test_vp_pipeline.py`](test_vp_pipeline.py)** | **PRIMARY** - Complete pipeline testing | `python test_vp_pipeline.py` |
| **[`test_plotting_module.py`](test_plotting_module.py)** | **SECONDARY** - Plotting functionality testing | `python test_plotting_module.py` |

**Test Categories**:
- ✅ Import verification
- ✅ Configuration validation
- ✅ Model architecture checks
- ✅ Data processing validation
- ✅ Pipeline integration tests
- ✅ Plotting functionality validation

**Usage**:
```bash
# Run main pipeline tests
python test_vp_pipeline.py

# Run plotting tests
python test_plotting_module.py

# Run both via console commands (if package installed)
vp-test
```

## 🧹 Repository Cleanup and Archives

### Cleanup Process Overview

The repository has undergone systematic cleanup to improve organization and maintainability. All removed files and folders have been carefully archived in the `archives/` directory for recovery purposes.

### Archive Structure and Recovery

**Archived Files Location**: `archives/`
- **cleaned_files/**: Timestamped cleanup phases with original directory structures
- **metadata/**: CSV tracking files for cleanup history and protected files
- **Individual files**: Standalone archived files (e.g., old visualization scripts)

**Key Archived Components**:

| Archived Item | Original Location | Archive Location | Status |
|---------------|-------------------|------------------|---------|
| `visualize_vp_results.py` | Root directory | `archives/visualize_vp_results.py` | **Archived** - Functionality integrated into main pipeline |
| Old `dataset.py` | Root directory | `archives/cleaned_files/20250906-1417_phase2/dataset.py` | **Archived** - Replaced by package version |
| Old `utils.py` | Root directory | `archives/cleaned_files/20250906-1417_phase2/utils.py` | **Archived** - Replaced by package version |
| Test outputs | Various locations | `archives/cleaned_files/20250906-1326_phase1/test_outputs/` | **Archived** - Historical test results |
| Cache files | Various locations | `archives/cleaned_files/20250906-1326_phase1/cache_files/` | **Archived** - Temporary data |

**Cleanup Documentation**:
- `1_List_of_cleaned.md`: Detailed list of cleaned items
- `CLEANUP_COMPLETION_REPORT.md`: Comprehensive cleanup report
- `POST_CLEANUP_INVENTORY.txt`: Final inventory after cleanup

### File Recovery Process

If you need to recover any archived file:

1. **Locate the file** in the appropriate archive directory
2. **Check the timestamp** to ensure you're getting the correct version
3. **Copy back to original location** if needed
4. **Update imports/references** if the file structure has changed

Example recovery:
```bash
# Recover archived visualization script
cp archives/visualize_vp_results.py ./

# Recover archived modules (with timestamp check)
cp archives/cleaned_files/20250906-1417_phase2/utils.py ./
```

### Archive Maintenance

- **DO NOT DELETE** the archives directory - it contains important recovery data
- **Archive metadata** is stored in CSV format for easy parsing
- **Cleanup phases** are timestamped for version tracking
- **Protected files** (like HDF5 data) were never moved and remain in place

## 🔄 Development Workflow

### Typical Development Cycle:

1. **Setup & Test**:
   ```bash
   python test_vp_pipeline.py
   ```

2. **Development**:
   - Modify components in `vp_predictor/` for package features
   - Modify root-level files for standalone features

3. **Testing**:
   ```bash
   python train_vp_improved.py --stage training  # Quick test
   ```

4. **Full Validation**:
   ```bash
   python train_vp_improved.py  # Complete pipeline
   ```

### Code Organization Principles:

- **Standalone Mode**: Root-level scripts work independently
- **Package Mode**: Enhanced features via `vp_predictor/` package
- **Backward Compatibility**: Existing code continues to work
- **Modular Design**: Clear separation of concerns

## 🔗 Key Dependencies

### Core Dependencies (Required):
- `torch>=1.8.0` - Deep learning framework
- `numpy>=1.19.0` - Numerical computing
- `h5py>=3.1.0` - HDF5 data handling
- `scikit-learn>=0.24.0` - ML utilities

### Optional Dependencies (Enhanced Features):
- `matplotlib>=3.3.0` - Visualization
- `pandas>=1.2.0` - Data analysis
- `lasio>=0.30` - LAS file support
- `tqdm>=4.50.0` - Progress bars

## 📚 Integration Points

### With MWLT System:
- Compatible with existing MWLT data formats
- Maintains familiar API interfaces
- Supports existing workflow patterns

### Standalone Usage:
- Self-contained execution
- Independent data processing
- Complete result generation

## 🔮 Future Development

### Phase 1 (Current):
- ✅ Stable VP prediction pipeline
- ✅ Modular package architecture
- ✅ Comprehensive testing

### Phase 2 (Planned):
- 🔄 Enhanced multi-curve support
- 🔄 Advanced configuration templates
- 🔄 Performance optimizations

### Phase 3 (Future):
- 🎯 Web interface integration
- 🎯 Real-time prediction APIs
- 🎯 Advanced visualization dashboards

---

## 📋 **CURRENT CODEBASE STATUS SUMMARY**

### ✅ **CONFIRMED MAIN PIPELINE COMPONENTS**

| **Priority** | **Script** | **Location** | **Status** | **Purpose** |
|--------------|------------|--------------|------------|-------------|
| **🎯 PRIMARY** | `train_vp_improved.py` | Root | ✅ **ACTIVE** | Complete 3-stage VP prediction pipeline |
| **📊 SECONDARY** | `plot_vp_improved.py` | Root | ✅ **ACTIVE** | Visualization functions library |
| **📊 UTILITY** | `visualize_vp_results.py` | Root | ✅ **ACTIVE** | Standalone visualization script |
| **🧪 TESTING** | `test_vp_pipeline.py` | Root | ✅ **ACTIVE** | Main pipeline testing suite |
| **🧪 TESTING** | `test_plotting_module.py` | Root | ✅ **ACTIVE** | Plotting functionality testing |

### 📦 **PACKAGE COMPONENTS (vp_predictor/)**

| **Component** | **Status** | **Main Function** |
|---------------|------------|-------------------|
| `vp_model_improved.py` | ✅ **ACTIVE** | VP model architectures (imported by main pipeline) |
| `model.py` | ✅ **ACTIVE** | Base transformer components + performance analysis |
| `las_processor.py` | ✅ **ACTIVE** | Data processing + test utilities |
| `predictor.py` | ✅ **ACTIVE** | High-level prediction API |
| `api/predictor.py` | ✅ **ACTIVE** | General-purpose prediction interface |

### 🎯 **ARCHITECTURE HIGHLIGHTS**

- **✅ MODULAR DESIGN**: Root-level scripts for main usage, package for components
- **✅ BACKWARD COMPATIBILITY**: All existing functionality preserved
- **✅ PROFESSIONAL PIPELINE**: Complete 3-stage workflow with proper logging
- **✅ COMPREHENSIVE TESTING**: Multiple test suites for different components
- **✅ FLEXIBLE VISUALIZATION**: Both integrated and standalone plotting options
- **✅ PACKAGE INSTALLATION**: Console commands available via `pip install -e .`

### 🚀 **RECOMMENDED USAGE PATTERNS**

1. **🎯 MAIN USAGE**: `python train_vp_improved.py` (complete pipeline)
2. **📊 VISUALIZATION ONLY**: Use plotting functions from `plot_vp_improved.py` (integrated with pipeline)
3. **🧪 TESTING**: `python test_vp_pipeline.py` (verify setup)
4. **📦 PACKAGE MODE**: `vp-train` and `vp-test` (after installation)

---

**📖 For detailed usage instructions, see**: [`VP_QUICK_START_GUIDE.md`](VP_QUICK_START_GUIDE.md)

**🚀 To get started immediately**: `python train_vp_improved.py`

**🧪 To verify setup**: `python test_vp_pipeline.py`