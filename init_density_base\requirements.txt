# Initial Density Prediction Module - Requirements
# Core dependencies for the self-contained density prediction system

# Deep Learning Framework
torch>=1.9.0
torchvision>=0.10.0

# Scientific Computing
numpy>=1.20.0
scipy>=1.7.0

# Data Handling
h5py>=3.0.0
pandas>=1.3.0

# Visualization
matplotlib>=3.3.0
seaborn>=0.11.0

# Machine Learning Utilities
scikit-learn>=0.24.0

# Model profiling and analysis
thop>=0.1.1  # For model FLOPs and parameter counting

# Optional but recommended
tqdm>=4.60.0  # Progress bars
tensorboard>=2.5.0  # Training monitoring (optional)

# Development and Testing (optional)
pytest>=6.0.0
jupyter>=1.0.0
ipython>=7.0.0

# Note: Adjust versions based on your Python version and system requirements
# For CUDA support, install appropriate PyTorch version from https://pytorch.org/
