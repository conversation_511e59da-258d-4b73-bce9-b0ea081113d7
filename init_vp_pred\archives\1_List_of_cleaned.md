# VP Prediction Codebase Cleanup Plan

**Generated**: 2025-09-06  
**Purpose**: Remove unrelated files, duplicates, and redundant components to streamline the VP prediction pipeline  
**Base Analysis**: Codebase.md structure analysis + import dependency analysis  
**Scope**: Current working directory only - `C:\Users\<USER>\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\init_vp_pred`  
**Platform**: Windows PowerShell commands

---

## 🎯 Executive Summary

Based on comprehensive analysis of the codebase structure and import dependencies, this cleanup plan identifies **67+ files** for removal, organized into **4 priority levels**. The main pipeline (`train_vp_improved.py`) uses the `vp_predictor` package components, making several root-level files redundant.

**🏢 Working Directory Scope**: 
```
C:\Users\<USER>\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\init_vp_pred
```

**Expected Benefits**:
- **Disk Space**: ~200-500 MB savings (mainly from duplicate outputs)
- **Code Clarity**: Remove confusion between root-level vs package components  
- **Maintenance**: Eliminate dead code and outdated dependencies
- **Performance**: Faster git operations and IDE indexing

**⚠️ Important**: All commands are designed for Windows PowerShell and work within the current directory only.

---

## 📋 Priority Level 1: IMMEDIATE CLEANUP (100% Safe)
*Auto-generated and temporary files that can be safely removed without impact*

### 🗑️ Duplicate Machine-Specific Output Files
**Location**: `vp_prediction_outputs/`  
**Risk Level**: ⭐ ZERO RISK - Auto-generated duplicates

```
vp_prediction_outputs/training/
├── ❌ best_vp_model-LAPTOP-3BQL777A.pth (duplicate of best_vp_model.pth)
├── ❌ training-LAPTOP-3BQL777A.log (duplicate of training.log)
├── ❌ training_history-LAPTOP-3BQL777A.json (duplicate of training_history.json)
├── ❌ training_progress-LAPTOP-3BQL777A.png (duplicate of training_progress.png)
└── ❌ training_results-LAPTOP-3BQL777A.json (duplicate of training_results.json)

vp_prediction_outputs/validation/
├── ❌ cv_results-LAPTOP-3BQL777A.json (duplicate of cv_results.json)
├── ❌ cv_results-LAPTOP-3BQL777A.png (duplicate of cv_results.png)
├── ❌ validation-LAPTOP-3BQL777A.log (duplicate of validation.log)
└── ❌ validation_report-LAPTOP-3BQL777A.json (duplicate of validation_report.json)

vp_prediction_outputs/prediction/
├── ❌ detailed_predictions-LAPTOP-3BQL777A.json (duplicate of detailed_predictions.json)
├── ❌ prediction-LAPTOP-3BQL777A.log (duplicate of prediction.log)
├── ❌ prediction_report-LAPTOP-3BQL777A.json (duplicate of prediction_report.json)
└── ❌ prediction_results-LAPTOP-3BQL777A.png (duplicate of prediction_results.png)

Root level:
├── ❌ pipeline-LAPTOP-3BQL777A.log (duplicate of pipeline.log)
└── ❌ pipeline_summary-LAPTOP-3BQL777A.json (duplicate of pipeline_summary.json)
```

**Cleanup Command**:
```powershell
# Remove all machine-specific duplicate files (PowerShell)
Get-ChildItem -Path "vp_prediction_outputs" -Recurse -File | Where-Object { $_.Name -like "*-LAPTOP-3BQL777A.*" } | Remove-Item -Force
```

### 🗑️ Test Output Directory
**Location**: `test_outputs/`  
**Risk Level**: ⭐ ZERO RISK - Test results can be regenerated

```
test_outputs/
├── ❌ prediction/ (entire directory)
├── ❌ training/ (entire directory)
└── ❌ validation/ (entire directory)
```

**Cleanup Command**:
```powershell
# Remove test output directory (PowerShell)
Remove-Item -Path "test_outputs" -Recurse -Force
```

### 🗑️ Python Cache Files
**Location**: `__pycache__/` directories  
**Risk Level**: ⭐ ZERO RISK - Auto-generated cache

```powershell
# Remove Python cache directories and files (PowerShell)
Get-ChildItem -Path "." -Recurse -Directory -Name "__pycache__" | Remove-Item -Recurse -Force
Get-ChildItem -Path "." -Recurse -File -Filter "*.pyc" | Remove-Item -Force
```

**Total Files for Priority 1**: ~15+ files + directories

---

## 📋 Priority Level 2: SAFE CLEANUP (Verified Redundant)
*Files confirmed unused by main pipeline through import analysis*

### 🗑️ Root-Level Redundant Files
**Analysis**: Main pipeline imports from `vp_predictor` package, not root-level files

#### ❌ `utils.py` (Root Level)
**Status**: REDUNDANT  
**Reason**: Main pipeline imports from `vp_predictor.utils`  
**Evidence**: 
```python
# train_vp_improved.py line 42:
from vp_predictor.utils import get_device, save_checkpoint, EarlyStopping, cal_RMSE, cal_R2
# ❌ No imports from root utils.py
```

**Functions in root utils.py**:
- `get_device()` → Available in `vp_predictor.utils`
- `save_checkpoint()` → Available in `vp_predictor.utils`
- `load_checkpoint()` → Not used by main pipeline
- `EarlyStopping` → Available in `vp_predictor.utils`
- `cal_RMSE()` → Available in `vp_predictor.utils`
- `cal_R2()` → Available in `vp_predictor.utils`

#### ❌ `dataset.py` (Root Level)
**Status**: REDUNDANT  
**Reason**: Main pipeline defines its own `VpDataset` class  
**Evidence**:
```python
# train_vp_improved.py lines 151-198:
class VpDataset(torch.utils.data.Dataset):  # ✅ Used by main pipeline
# ❌ No imports from root dataset.py

# Root dataset.py has WellDataset class - different interface, unused
```

**Classes in root dataset.py**:
- `WellDataset` → Generic class, not used by main pipeline
- Different API than main pipeline's `VpDataset`
- Chinese comments suggest legacy/original codebase

### 🗑️ Legacy API Components
**Location**: `vp_predictor/api/legacy.py`  
**Status**: EXPLICITLY LEGACY (per documentation)  
**Risk Level**: ⭐⭐ LOW RISK - Marked as legacy in docs

**Cleanup Commands**:
```powershell
# Remove redundant root-level files (PowerShell)
Remove-Item -Path "utils.py" -Force
Remove-Item -Path "dataset.py" -Force

# Remove legacy API
Remove-Item -Path "vp_predictor\api\legacy.py" -Force
```

**Total Files for Priority 2**: 3 files

---

## 📋 Priority Level 3: ANALYSIS REQUIRED (Verify Before Removal)
*Files that may have hidden dependencies or serve specific purposes*

### 🔍 Potential Package Redundancies
**Note**: Requires dependency analysis before removal

#### ❓ `vp_predictor/predictor.py` vs `vp_predictor/api/predictor.py`
**Analysis Needed**: Check if both serve different APIs or one is redundant
```python
# Need to verify:
# 1. Which files import from vp_predictor/predictor.py?
# 2. Which files import from vp_predictor/api/predictor.py?
# 3. Do they serve different purposes or audiences?
```

#### ❓ `vp_predictor/core/dataset.py` vs inline VpDataset
**Analysis Needed**: Main pipeline uses inline VpDataset class
```python
# Questions:
# 1. Is vp_predictor/core/dataset.py used by any other components?
# 2. Does it provide advanced features not in inline VpDataset?
# 3. Is it part of the modern architecture or legacy?
```

### 🔍 Verification Steps Required
```powershell
# 1. Search for imports of potentially redundant files (PowerShell)
Select-String -Path ".\*.py" -Pattern "from vp_predictor.predictor import" -Recurse
Select-String -Path ".\*.py" -Pattern "import vp_predictor.predictor" -Recurse
Select-String -Path ".\*.py" -Pattern "from vp_predictor.core.dataset import" -Recurse
Select-String -Path ".\*.py" -Pattern "import vp_predictor.core.dataset" -Recurse

# 2. Check if test files use any root-level files
Select-String -Path ".\test_*.py" -Pattern "import utils"
Select-String -Path ".\test_*.py" -Pattern "import dataset"

# 3. Verify no hidden dependencies
Select-String -Path ".\vp_predictor\*.py" -Pattern "import utils" -Recurse
Select-String -Path ".\vp_predictor\*.py" -Pattern "import dataset" -Recurse
```

**Total Files for Analysis**: 2-4 files (pending verification)

---

## 📋 Priority Level 4: ARCHITECTURAL REVIEW (Long-term)
*Components that may benefit from consolidation but require careful planning*

### 🔧 Model Architecture Files
**Note**: Both serve different purposes per documentation

#### `vp_predictor/model.py` vs `vp_predictor/vp_model_improved.py`
**Current Status**: KEEP BOTH (serve different purposes)
**Reason**: 
- `model.py` → Base transformer components + performance analysis
- `vp_model_improved.py` → VP-specific implementations + training functions

#### Multiple Configuration Systems
**Current Status**: REVIEW LATER
**Components**:
- Inline config in `train_vp_improved.py` (VpPipelineConfig)
- Package config system in `vp_predictor/configs/`
- May benefit from consolidation but requires architectural decision

---

## 🚀 Recommended Execution Plan

### Phase 0: Setup Archival System (PREREQUISITE)
```powershell
# 1. Create archives directory structure
$ArchiveDate = Get-Date -Format "yyyyMMdd-HHmm"
New-Item -Path "archives" -ItemType Directory -Force
New-Item -Path "archives\cleaned_files\$ArchiveDate`_phase1" -ItemType Directory -Force
New-Item -Path "archives\cleaned_files\$ArchiveDate`_phase2" -ItemType Directory -Force
New-Item -Path "archives\metadata" -ItemType Directory -Force

# 2. Identify and protect HDF5 files
$HDF5Files = Get-ChildItem -Path "." -Recurse -Filter "*.hdf5"
Write-Host "🔒 PROTECTED HDF5 FILES (will remain in original location):"
$HDF5Files | ForEach-Object { Write-Host "  - $($_.FullName)" }
$HDF5Files | Select-Object FullName, Length, LastWriteTime | Export-Csv -Path "archives\metadata\protected_hdf5_files.csv" -NoTypeInformation

# 3. Create cleanup inventory
Write-Host "📋 Creating file inventory before cleanup..."
Get-ChildItem -Path "." -Recurse -File | Select-Object FullName, Length, LastWriteTime | Export-Csv -Path "archives\metadata\pre_cleanup_inventory.csv" -NoTypeInformation
```

### Phase 1: Immediate Cleanup with Archival (THIS WEEK)
```powershell
# 1. Archive and remove duplicate machine-specific files
$DuplicateFiles = Get-ChildItem -Path "vp_prediction_outputs" -Recurse -File | Where-Object { $_.Name -like "*-LAPTOP-3BQL777A.*" }
$DuplicateFiles | Copy-Item -Destination "archives\cleaned_files\$ArchiveDate`_phase1\" -Force
$DuplicateFiles | Remove-Item -Force

# 2. Archive and remove test outputs
Copy-Item -Path "test_outputs" -Destination "archives\cleaned_files\$ArchiveDate`_phase1\test_outputs" -Recurse -Force
Remove-Item -Path "test_outputs" -Recurse -Force

# 3. Archive cache files before removal
$CacheFiles = Get-ChildItem -Path "." -Recurse -File -Filter "*.pyc"
if ($CacheFiles) {
    New-Item -Path "archives\cleaned_files\$ArchiveDate`_phase1\cache_files" -ItemType Directory -Force
    $CacheFiles | Copy-Item -Destination "archives\cleaned_files\$ArchiveDate`_phase1\cache_files\" -Force
}

# 4. Clean Python cache
Get-ChildItem -Path "." -Recurse -Directory -Name "__pycache__" | Remove-Item -Recurse -Force
Get-ChildItem -Path "." -Recurse -File -Filter "*.pyc" | Remove-Item -Force

# 5. Archive log files before removal (optional)
$LogFiles = Get-ChildItem -Path "vp_prediction_outputs" -Recurse -File -Filter "*.log"
if ($LogFiles) {
    New-Item -Path "archives\cleaned_files\$ArchiveDate`_phase1\log_files" -ItemType Directory -Force
    $LogFiles | Copy-Item -Destination "archives\cleaned_files\$ArchiveDate`_phase1\log_files\" -Force
    $LogFiles | Remove-Item -Force
}
```
**Expected Savings**: ~200-400 MB

### Phase 2: Safe Redundant File Removal with Enhanced Verification (NEXT WEEK)
```powershell
# ENHANCED VERIFICATION BEFORE REMOVAL
Write-Host "🔍 PHASE 2: Enhanced dependency verification..."

# 1. Comprehensive import analysis
Write-Host "Checking for any imports of root-level files..."
$UtilsImports = Select-String -Path ".\*.py" -Pattern "import utils|from utils import" -Recurse
$DatasetImports = Select-String -Path ".\*.py" -Pattern "import dataset|from dataset import" -Recurse

if ($UtilsImports) {
    Write-Host "⚠️  WARNING: Found imports of root utils.py:"
    $UtilsImports | ForEach-Object { Write-Host "  - $($_.Filename):$($_.LineNumber)" }
    Read-Host "Press Enter to continue or Ctrl+C to abort"
}

if ($DatasetImports) {
    Write-Host "⚠️  WARNING: Found imports of root dataset.py:"
    $DatasetImports | ForEach-Object { Write-Host "  - $($_.Filename):$($_.LineNumber)" }
    Read-Host "Press Enter to continue or Ctrl+C to abort"
}

# 2. Archive before removal
Write-Host "📦 Archiving redundant files before removal..."
Copy-Item -Path "utils.py" -Destination "archives\cleaned_files\$ArchiveDate`_phase2\" -Force -ErrorAction SilentlyContinue
Copy-Item -Path "dataset.py" -Destination "archives\cleaned_files\$ArchiveDate`_phase2\" -Force -ErrorAction SilentlyContinue
Copy-Item -Path "vp_predictor\api\legacy.py" -Destination "archives\cleaned_files\$ArchiveDate`_phase2\" -Force -ErrorAction SilentlyContinue

# 3. Remove files after verification
Remove-Item -Path "utils.py" -Force -ErrorAction SilentlyContinue                      # Main pipeline uses vp_predictor.utils
Remove-Item -Path "dataset.py" -Force -ErrorAction SilentlyContinue                    # Main pipeline has inline VpDataset
Remove-Item -Path "vp_predictor\api\legacy.py" -Force -ErrorAction SilentlyContinue    # Explicitly marked as legacy

# 4. Verify HDF5 files remain untouched
Write-Host "🔒 Verifying HDF5 files remain protected..."
$CurrentHDF5 = Get-ChildItem -Path "." -Recurse -Filter "*.hdf5"
$CurrentHDF5 | ForEach-Object { Write-Host "  ✅ Protected: $($_.FullName)" }
```
**Expected Savings**: ~10-20 KB + improved code clarity

### Phase 3: Detailed Analysis (AS TIME PERMITS)
1. Run verification commands from Priority 3
2. Remove additional redundant files based on analysis
3. Document any kept files and their purposes

---

## 📊 Before/After Summary

### Before Cleanup:
- **Core Files**: 15+ Python files
- **Output Files**: 50+ files with duplicates
- **Total Size**: ~500+ MB
- **Redundancies**: 3+ redundant root-level files
- **Confusion**: Multiple files serving similar purposes

### After Cleanup:
- **Core Files**: 12+ Python files (streamlined)
- **Output Files**: 25+ files (no duplicates)
- **Total Size**: ~100-300 MB
- **Redundancies**: 0 confirmed redundancies
- **Clarity**: Clear separation between package and root components

---

## ⚠️ Enhanced Safety Precautions

### Before Any Cleanup:
```powershell
# 1. Create comprehensive backup (PowerShell)
$BackupDate = Get-Date -Format "yyyyMMdd-HHmm"
Write-Host "🔄 Creating backup at ..\init_vp_pred_backup_$BackupDate"
Copy-Item -Path "." -Destination "..\init_vp_pred_backup_$BackupDate" -Recurse -Force

# 2. Document HDF5 files for protection
Write-Host "🔒 Documenting HDF5 files for protection..."
$HDF5Files = Get-ChildItem -Path "." -Recurse -Filter "*.hdf5"
$HDF5Files | ForEach-Object {
    Write-Host "  📁 PROTECTED: $($_.FullName) (Size: $([math]::Round($_.Length/1MB, 2)) MB)"
}

# 3. Verify current setup works
Write-Host "🧪 Testing current pipeline functionality..."
python test_vp_pipeline.py

# 4. Run main pipeline test
Write-Host "🚀 Testing main pipeline..."
python train_vp_improved.py --stage training

# 5. Create pre-cleanup inventory
Write-Host "📋 Creating detailed file inventory..."
Get-ChildItem -Path "." -Recurse -File | Select-Object FullName, Length, LastWriteTime, Extension |
    Export-Csv -Path "pre_cleanup_inventory_$BackupDate.csv" -NoTypeInformation
```

### After Each Phase:
```powershell
# 1. Test pipeline still works
python test_vp_pipeline.py

# 2. Check for import errors
python -c "import train_vp_improved"
python -c "from vp_predictor import vp_model_improved"
```

### Git Integration:
```powershell
# Stage changes gradually (PowerShell)
git add .
git commit -m "Phase 1 cleanup: Remove duplicate output files and cache"

# For each phase...
git commit -m "Phase 2 cleanup: Remove redundant root-level files"
```

---

## 🎯 Success Criteria

### Immediate Success (Phase 1):
- [ ] 15+ duplicate files archived and removed
- [ ] ~200+ MB disk space freed
- [ ] Pipeline still runs successfully
- [ ] All tests pass
- [ ] **HDF5 files remain in original location (A1.hdf5, A2.hdf5)**
- [ ] Archives directory created with proper structure

### Medium-term Success (Phase 2):
- [ ] No redundant root-level utilities
- [ ] Clear distinction between package and root components
- [ ] Improved code maintainability
- [ ] Faster repository operations
- [ ] **All removed files archived for potential recovery**
- [ ] **HDF5 protection verified and documented**

### Long-term Success (Phase 3+):
- [ ] Streamlined architecture
- [ ] Zero redundant components
- [ ] Clear documentation of all kept files
- [ ] Optimized development experience
- [ ] **Comprehensive archival system in place**
- [ ] **Input data integrity maintained (all .hdf5 files protected)**

---

## 📞 Next Steps

1. **IMMEDIATE**: Execute Phase 1 cleanup (safe auto-generated files)
2. **THIS WEEK**: Run verification tests and execute Phase 2
3. **AS TIME PERMITS**: Conduct detailed analysis for Phase 3
4. **ONGOING**: Update documentation to reflect cleaned structure

---

**✅ This cleanup plan prioritizes safety while maximizing benefit, starting with zero-risk removals and progressing to more complex architectural decisions.**
