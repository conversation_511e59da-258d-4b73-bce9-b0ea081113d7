"""
Comprehensive VP (Acoustic Velocity) Prediction Pipeline
A complete three-stage workflow for VP prediction with proper organization and integration

Features:
- Stage 1: Training VP models with proper checkpointing and monitoring
- Stage 2: Comprehensive validation with cross-validation and performance metrics
- Stage 3: Model inference with prediction visualization and analysis
- Organized output directory structure
- Integration with vp_predictor package components
- Professional logging, error handling, and progress tracking

Author: Refactored for MWLT System
Date: 2025-08-23
"""
import os
import sys
import argparse
import time
import json
import logging
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any
from datetime import datetime

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import numpy as np
import h5py
import matplotlib
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split, KFold
from sklearn.metrics import r2_score, mean_absolute_error, mean_squared_error
from scipy import stats
from tqdm import tqdm

# Import from vp_predictor package
from vp_predictor.vp_model_improved import MWLT_Vp_Small, MWLT_Vp_Base, MWLT_Vp_Large, VpDataNormalizer, VpLoss
from vp_predictor.utils import get_device, save_checkpoint, EarlyStopping, cal_RMSE, cal_R2
from vp_predictor.las_processor import LASProcessor
# Import plotting functions
import plot_vp_improved as plot_vp
USING_VP_PREDICTOR_PACKAGE = True

# ============================================================================
# CONFIGURATION AND SETUP
# ============================================================================

class VpPipelineConfig:
    """Configuration management for VP prediction pipeline"""

    def __init__(self, config_dict: Optional[Dict] = None):
        """Initialize configuration with defaults and optional overrides"""
        self.config = self._get_default_config()
        if config_dict:
            self._update_config(config_dict)

    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration parameters"""
        return {
            # Model configuration
            'model': {
                'type': 'base',  # 'small', 'base', 'large'
                'input_channels': 4,  # GR, CNL, DEN, RLLD
                'output_channels': 1,  # AC (Vp)
                'feature_num': 64,
                'sequence_length': 720,
                'effect_length': 640
            },

            # Training configuration
            'training': {
                'batch_size': 8,
                'learning_rate': 1e-4,
                'weight_decay': 1e-5,
                'epochs': 200,
                'patience': 50,
                'validation_split': 0.2,
                'random_seed': 42,
                'device': 0,
                'resume_training': False
            },

            # Validation configuration
            'validation': {
                'cv_folds': 5,
                'monte_carlo_samples': 50,
                'confidence_level': 0.95,
                'metrics': ['r2', 'rmse', 'mae']
            },

            # Data configuration
            'data': {
                'input_curves': ['GR', 'CNL', 'DEN', 'RLLD'],
                'target_curve': 'AC',
                'data_augmentation': True,
                'augmentation_factor': 2.0,
                'quality_threshold': 0.8
            },

            # Output configuration
            'output': {
                'base_dir': 'vp_prediction_outputs',
                'save_plots': True,
                'save_models': True,
                'save_predictions': True,
                'plot_format': 'png',
                'plot_dpi': 300,
                'interactive_plots': False
            }
        }

    def _update_config(self, config_dict: Dict):
        """Update configuration with provided dictionary"""
        def update_nested_dict(base_dict, update_dict):
            for key, value in update_dict.items():
                if key in base_dict and isinstance(base_dict[key], dict) and isinstance(value, dict):
                    update_nested_dict(base_dict[key], value)
                else:
                    base_dict[key] = value

        update_nested_dict(self.config, config_dict)

    def get(self, key_path: str, default=None):
        """Get configuration value using dot notation (e.g., 'model.type')"""
        keys = key_path.split('.')
        value = self.config
        for key in keys:
            if isinstance(value, dict) and key in value:
                value = value[key]
            else:
                return default
        return value

    def save(self, filepath: str):
        """Save configuration to JSON file"""
        with open(filepath, 'w') as f:
            json.dump(self.config, f, indent=2)

    @classmethod
    def load(cls, filepath: str):
        """Load configuration from JSON file"""
        with open(filepath, 'r') as f:
            config_dict = json.load(f)
        return cls(config_dict)


class VpDataset(torch.utils.data.Dataset):
    """Enhanced dataset for VP prediction with proper normalization and augmentation"""

    def __init__(self, input_data: np.ndarray, target_data: np.ndarray,
                 normalizer: VpDataNormalizer, config: VpPipelineConfig,
                 transform: bool = False):
        """
        Initialize VP dataset

        Args:
            input_data: Input curve data [N, 4, L] for GR, CNL, DEN, RLLD
            target_data: Target AC data [N, L]
            normalizer: VP data normalizer
            config: Pipeline configuration
            transform: Whether to apply data augmentation
        """
        self.input_data = input_data
        self.target_data = target_data
        self.normalizer = normalizer
        self.config = config
        self.transform = transform

        self.total_seqlen = config.get('model.sequence_length', 720)
        self.effect_seqlen = config.get('model.effect_length', 640)

    def __len__(self) -> int:
        return len(self.input_data)

    def __getitem__(self, idx: int) -> Tuple[torch.Tensor, torch.Tensor]:
        """Get dataset item with proper normalization"""
        # Get input curves and target
        inputs = self.input_data[idx]  # Shape: [4, L] for GR, CNL, DEN, RLLD
        target = self.target_data[idx]  # Shape: [L] for AC

        # Apply data augmentation if requested
        if self.transform and self.total_seqlen > self.effect_seqlen:
            start_idx = np.random.randint(0, self.total_seqlen - self.effect_seqlen + 1)
            inputs = inputs[:, start_idx:start_idx + self.effect_seqlen]
            target = target[start_idx:start_idx + self.effect_seqlen]
        else:
            inputs = inputs[:, :self.effect_seqlen]
            target = target[:self.effect_seqlen]

        # Normalize target values for training using VpDataNormalizer
        target_tensor = torch.FloatTensor(target)
        target_normalized = self.normalizer.normalize_vp(target_tensor)

        return torch.FloatTensor(inputs), target_normalized.unsqueeze(0)

# ============================================================================
# DATA PROCESSING AND UTILITIES
# ============================================================================

class VpDataProcessor:
    """Enhanced data processor for VP prediction with comprehensive data handling"""

    def __init__(self, config: VpPipelineConfig):
        """Initialize data processor with configuration"""
        self.config = config
        self.processor = LASProcessor()
        self.normalizer = VpDataNormalizer()
        self.logger = self._setup_logger()

    def _setup_logger(self) -> logging.Logger:
        """Setup logger for data processing"""
        logger = logging.getLogger('VpDataProcessor')
        logger.setLevel(logging.INFO)
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        return logger

    def find_data_files(self) -> List[str]:
        """Auto-detect the location of A1.hdf5 and A2.hdf5 files"""
        possible_paths = [
            '.',  # Current directory
            '..',  # Parent directory
            Path(__file__).parent,  # Script directory
            Path(__file__).parent.parent,  # Project root
            # Add more paths as needed
            r"C:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\main\Transformer",
        ]

        found_files = []
        for base_path in possible_paths:
            base_path = Path(base_path)
            a1_path = base_path / 'A1.hdf5'
            a2_path = base_path / 'A2.hdf5'

            if a1_path.exists() and a2_path.exists():
                found_files = [str(a1_path), str(a2_path)]
                self.logger.info(f"✅ Found data files in: {base_path.absolute()}")
                break

        if not found_files:
            self.logger.error("❌ Could not find A1.hdf5 and A2.hdf5 files!")
            self.logger.error("Searched in:")
            for path in possible_paths:
                self.logger.error(f"  - {Path(path).absolute()}")
            self.logger.error("\nPlease ensure A1.hdf5 and A2.hdf5 are in one of these locations.")

        return found_files

    def create_training_data(self) -> Tuple[np.ndarray, np.ndarray]:
        """Create enhanced training data with proper normalization and augmentation"""
        self.logger.info("Creating enhanced VP training data...")

        # Auto-detect data file locations
        input_files = self.find_data_files()
        if not input_files:
            return np.array([]), np.array([])

        all_inputs = []
        all_targets = []

        sequence_length = self.config.get('model.sequence_length', 720)
        augmentation_factor = self.config.get('data.augmentation_factor', 2.0)
        step_size = int(sequence_length / augmentation_factor)  # Overlap based on augmentation factor

        for file_path in input_files:
            if not Path(file_path).exists():
                self.logger.warning(f"File {file_path} not found, skipping...")
                continue

            self.logger.info(f"Processing {file_path}...")
            curves = self.processor.process_hdf5_to_las_format(file_path)

            if 'AC' not in curves:
                self.logger.warning(f"No AC curve found in {file_path}, skipping...")
                continue

            # Create multiple overlapping windows for data augmentation
            data_length = len(curves['AC'])
            num_windows = max(1, (data_length - sequence_length) // step_size + 1)

            self.logger.info(f"Creating {num_windows} windows from {data_length} samples")

            for i in range(num_windows):
                start_idx = i * step_size
                end_idx = start_idx + sequence_length

                if end_idx > data_length:
                    start_idx = data_length - sequence_length
                    end_idx = data_length

                # Extract window
                window_curves = {}
                for curve_name, data in curves.items():
                    window_curves[curve_name] = data[start_idx:end_idx]

                # Validate data quality
                if not self._validate_window_quality(window_curves):
                    continue

                # Normalize inputs
                input_features = self._normalize_input_curves(window_curves, sequence_length)

                # Target (AC) - keep in original units for normalization in dataset
                target = window_curves.get('AC', np.zeros(sequence_length))

                all_inputs.append(np.array(input_features))
                all_targets.append(target)

        self.logger.info(f"Created {len(all_inputs)} training samples")
        return np.array(all_inputs), np.array(all_targets)

    def _validate_window_quality(self, window_curves: Dict[str, np.ndarray]) -> bool:
        """Validate data quality for a window"""
        quality_threshold = self.config.get('data.quality_threshold', 0.8)

        for curve_name in self.config.get('data.input_curves', ['GR', 'CNL', 'DEN', 'RLLD']):
            if curve_name in window_curves:
                data = window_curves[curve_name]
                # Check for too many NaN or zero values
                valid_ratio = np.sum(~np.isnan(data) & (data != 0)) / len(data)
                if valid_ratio < quality_threshold:
                    return False

        # Check target curve quality
        target_curve = self.config.get('data.target_curve', 'AC')
        if target_curve in window_curves:
            data = window_curves[target_curve]
            valid_ratio = np.sum(~np.isnan(data) & (data > 0)) / len(data)
            if valid_ratio < quality_threshold:
                return False

        return True

    def _normalize_input_curves(self, window_curves: Dict[str, np.ndarray],
                               sequence_length: int) -> List[np.ndarray]:
        """Normalize input curves with proper handling"""
        input_features = []
        input_curves = self.config.get('data.input_curves', ['GR', 'CNL', 'DEN', 'RLLD'])

        for curve_name in input_curves:
            if curve_name in window_curves:
                data = torch.FloatTensor(window_curves[curve_name])

                # Handle NaN values
                data = torch.nan_to_num(data, nan=0.0)

                if curve_name == 'RLLD':
                    # Log transform for resistivity with proper handling
                    data = torch.log10(torch.clamp(data, min=0.1))
                    normalized = (data - 1.0) / 2.0
                else:
                    # Use normalizer statistics
                    stats = self.normalizer.input_stats.get(curve_name, {'mean': 0.0, 'std': 1.0})
                    normalized = (data - stats['mean']) / stats['std']
                    normalized = torch.clamp(normalized, -3, 3) / 3

                input_features.append(normalized.numpy())
            else:
                # Fill missing curves with zeros
                input_features.append(np.zeros(sequence_length))

        return input_features

# ============================================================================
# STAGE 1: VP TRAINING MANAGER
# ============================================================================

class VpTrainingManager:
    """Comprehensive training manager for VP prediction models"""

    def __init__(self, config: VpPipelineConfig, output_dir: Path):
        """Initialize training manager"""
        self.config = config
        self.output_dir = output_dir
        self.training_dir = output_dir / 'training'
        self.training_dir.mkdir(parents=True, exist_ok=True)

        self.logger = self._setup_logger()
        self.data_processor = VpDataProcessor(config)
        self.device = get_device(config.get('training.device', 0))

        # Training state
        self.model = None
        self.optimizer = None
        self.criterion = None
        self.early_stopping = None
        self.training_history = {'train_loss': [], 'val_loss': [], 'val_r2': [], 'val_rmse': []}

    def _setup_logger(self) -> logging.Logger:
        """Setup logger for training"""
        logger = logging.getLogger('VpTrainingManager')
        logger.setLevel(logging.INFO)
        if not logger.handlers:
            # Ensure training directory exists before creating log file
            self.training_dir.mkdir(parents=True, exist_ok=True)

            # File handler
            log_file = self.training_dir / 'training.log'
            file_handler = logging.FileHandler(log_file, encoding='utf-8')
            file_formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            file_handler.setFormatter(file_formatter)
            logger.addHandler(file_handler)

            # Console handler
            console_handler = logging.StreamHandler()
            console_formatter = logging.Formatter('%(levelname)s - %(message)s')
            console_handler.setFormatter(console_formatter)
            logger.addHandler(console_handler)
        return logger

    def prepare_data(self) -> Tuple[DataLoader, DataLoader]:
        """Prepare training and validation data loaders"""
        self.logger.info("Preparing training data...")

        # Create training data
        input_data, target_data = self.data_processor.create_training_data()

        if len(input_data) == 0:
            raise ValueError("No training data created! Check data files and configuration.")

        # Split data
        val_split = self.config.get('training.validation_split', 0.2)
        random_seed = self.config.get('training.random_seed', 42)

        train_inputs, val_inputs, train_targets, val_targets = train_test_split(
            input_data, target_data, test_size=val_split, random_state=random_seed
        )

        self.logger.info(f"Training samples: {len(train_inputs)}")
        self.logger.info(f"Validation samples: {len(val_inputs)}")

        # Create datasets
        normalizer = VpDataNormalizer()
        train_dataset = VpDataset(train_inputs, train_targets, normalizer, self.config, transform=True)
        val_dataset = VpDataset(val_inputs, val_targets, normalizer, self.config, transform=False)

        # Create data loaders
        batch_size = self.config.get('training.batch_size', 8)
        train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, num_workers=0)
        val_loader = DataLoader(val_dataset, batch_size=1, shuffle=False, num_workers=0)

        return train_loader, val_loader

    def create_model(self):
        """Create and initialize the VP prediction model"""
        model_type = self.config.get('model.type', 'base')

        self.logger.info(f"Creating {model_type} VP model...")

        if model_type == 'small':
            self.model = MWLT_Vp_Small()
        elif model_type == 'base':
            self.model = MWLT_Vp_Base()
        elif model_type == 'large':
            self.model = MWLT_Vp_Large()
        else:
            raise ValueError(f"Unknown model type: {model_type}")

        self.model = self.model.to(self.device)

        # Log model information
        total_params = sum(p.numel() for p in self.model.parameters())
        trainable_params = sum(p.numel() for p in self.model.parameters() if p.requires_grad)
        self.logger.info(f"Model created with {total_params:,} total parameters ({trainable_params:,} trainable)")

    def setup_training(self):
        """Setup training components (optimizer, loss, early stopping)"""
        learning_rate = self.config.get('training.learning_rate', 1e-4)
        weight_decay = self.config.get('training.weight_decay', 1e-5)

        self.optimizer = optim.Adam(self.model.parameters(), lr=learning_rate, weight_decay=weight_decay)
        self.criterion = VpLoss()

        # Early stopping
        patience = self.config.get('training.patience', 50)
        best_model_path = self.training_dir / 'best_vp_model.pth'
        self.early_stopping = EarlyStopping(patience=patience, path=str(best_model_path))

        self.logger.info(f"Training setup complete - LR: {learning_rate}, Weight Decay: {weight_decay}, Patience: {patience}")

    def train_model(self, train_loader: DataLoader, val_loader: DataLoader) -> Dict[str, Any]:
        """Execute the training loop"""
        epochs = self.config.get('training.epochs', 200)

        self.logger.info(f"Starting VP model training for {epochs} epochs on {self.device}")
        self.logger.info(f"Training samples: {len(train_loader.dataset)}, Validation samples: {len(val_loader.dataset)}")

        start_time = time.time()

        for epoch in range(1, epochs + 1):
            # Training phase
            train_loss = self._train_epoch(train_loader)

            # Validation phase
            val_loss, val_metrics = self._validate_epoch(val_loader)

            # Update history
            self.training_history['train_loss'].append(train_loss)
            self.training_history['val_loss'].append(val_loss)
            self.training_history['val_r2'].append(val_metrics['r2'])
            self.training_history['val_rmse'].append(val_metrics['rmse'])

            # Log progress
            self.logger.info(f"Epoch {epoch:3d}/{epochs}: "
                           f"Train Loss: {train_loss:.6f}, Val Loss: {val_loss:.6f}, "
                           f"R²: {val_metrics['r2']:.4f}, RMSE: {val_metrics['rmse']:.2f}")

            # Early stopping check
            checkpoint_data = {
                "model_state_dict": self.model.state_dict(),
                "optimizer_state_dict": self.optimizer.state_dict(),
                "config": self.config.config,
                "epoch": epoch,
                "train_loss": train_loss,
                "val_loss": val_loss,
                "loss": val_loss,  # Add 'loss' key for EarlyStopping compatibility
                "val_r2": val_metrics['r2'],
                "val_rmse": val_metrics['rmse'],
                "training_history": self.training_history
            }

            self.early_stopping(checkpoint_data, self.model)
            if self.early_stopping.early_stop:
                self.logger.info(f"Early stopping triggered at epoch {epoch}")
                break

        training_time = time.time() - start_time
        self.logger.info(f"Training completed in {training_time:.2f} seconds")

        return self._get_training_results()

    def _train_epoch(self, train_loader: DataLoader) -> float:
        """Execute one training epoch"""
        self.model.train()
        total_loss = 0.0

        progress_bar = tqdm(train_loader, desc="Training", leave=False)
        for inputs, targets in progress_bar:
            inputs, targets = inputs.to(self.device), targets.to(self.device)

            self.optimizer.zero_grad()
            outputs = self.model(inputs)
            loss = self.criterion(outputs, targets)
            loss.backward()
            self.optimizer.step()

            total_loss += loss.item()
            progress_bar.set_postfix({'loss': loss.item()})

        return total_loss / len(train_loader)

    def _validate_epoch(self, val_loader: DataLoader) -> Tuple[float, Dict[str, float]]:
        """Execute one validation epoch"""
        self.model.eval()
        total_loss = 0.0
        predictions = []
        actuals = []

        with torch.no_grad():
            for inputs, targets in val_loader:
                inputs, targets = inputs.to(self.device), targets.to(self.device)
                outputs = self.model(inputs)
                loss = self.criterion(outputs, targets)

                total_loss += loss.item()
                predictions.extend(outputs.cpu().numpy().flatten())
                actuals.extend(targets.cpu().numpy().flatten())

        val_loss = total_loss / len(val_loader)
        predictions = np.array(predictions)
        actuals = np.array(actuals)

        metrics = {
            'r2': cal_R2(predictions, actuals),
            'rmse': cal_RMSE(predictions, actuals),
            'mae': mean_absolute_error(actuals, predictions)
        }

        return val_loss, metrics

    def _get_training_results(self) -> Dict[str, Any]:
        """Get comprehensive training results"""
        return {
            'best_model_path': str(self.training_dir / 'best_vp_model.pth'),
            'training_history': self.training_history,
            'final_metrics': {
                'best_val_r2': max(self.training_history['val_r2']),
                'best_val_rmse': min(self.training_history['val_rmse']),
                'final_val_r2': self.training_history['val_r2'][-1] if self.training_history['val_r2'] else 0,
                'final_val_rmse': self.training_history['val_rmse'][-1] if self.training_history['val_rmse'] else float('inf')
            },
            'training_config': self.config.config
        }

    def _convert_to_json_serializable(self, obj):
        """Convert numpy types to JSON-serializable types"""
        if isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, dict):
            return {key: self._convert_to_json_serializable(value) for key, value in obj.items()}
        elif isinstance(obj, list):
            return [self._convert_to_json_serializable(item) for item in obj]
        else:
            return obj

    def save_training_results(self, results: Dict[str, Any]):
        """Save comprehensive training results"""
        # Convert results to JSON-serializable format
        serializable_results = self._convert_to_json_serializable(results)
        
        # Save training history
        history_file = self.training_dir / 'training_history.json'
        with open(history_file, 'w') as f:
            json.dump(serializable_results['training_history'], f, indent=2)

        # Save final results
        results_file = self.training_dir / 'training_results.json'
        with open(results_file, 'w') as f:
            json.dump({
                'final_metrics': serializable_results['final_metrics'],
                'training_config': serializable_results['training_config'],
                'best_model_path': serializable_results['best_model_path']
            }, f, indent=2)

        # Create training plots
        plot_vp.create_training_plots(
            serializable_results['training_history'],
            self.training_dir,
            self.config.config,
            self.config.get('output.interactive_plots', False)
        )

        self.logger.info(f"Training results saved to {self.training_dir}")


# ============================================================================
# STAGE 2: VP VALIDATION MANAGER
# ============================================================================

class VpValidationManager:
    """Comprehensive validation manager for VP prediction models"""

    def __init__(self, model_path: str, config: VpPipelineConfig, output_dir: Path):
        """Initialize validation manager"""
        self.model_path = model_path
        self.config = config
        self.output_dir = output_dir
        self.validation_dir = output_dir / 'validation'
        self.validation_dir.mkdir(parents=True, exist_ok=True)

        self.logger = self._setup_logger()
        self.data_processor = VpDataProcessor(config)
        self.device = get_device(config.get('training.device', 0))

        self.model = None
        self.normalizer = VpDataNormalizer()

    def _setup_logger(self) -> logging.Logger:
        """Setup logger for validation"""
        logger = logging.getLogger('VpValidationManager')
        logger.setLevel(logging.INFO)
        if not logger.handlers:
            # Ensure validation directory exists before creating log file
            self.validation_dir.mkdir(parents=True, exist_ok=True)

            # File handler
            log_file = self.validation_dir / 'validation.log'
            file_handler = logging.FileHandler(log_file, encoding='utf-8')
            file_formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            file_handler.setFormatter(file_formatter)
            logger.addHandler(file_handler)

            # Console handler
            console_handler = logging.StreamHandler()
            console_formatter = logging.Formatter('%(levelname)s - %(message)s')
            console_handler.setFormatter(console_formatter)
            logger.addHandler(console_handler)
        return logger

    def load_trained_model(self):
        """Load the trained VP model"""
        self.logger.info(f"Loading trained model from {self.model_path}")

        if not Path(self.model_path).exists():
            raise FileNotFoundError(f"Model file not found: {self.model_path}")

        # Load checkpoint
        try:
            # PyTorch 2.6+: default weights_only=True can break loading older checkpoints.
            # Explicitly set weights_only=False for trusted local checkpoints.
            checkpoint = torch.load(self.model_path, map_location=self.device, weights_only=False)
        except TypeError:
            # Older PyTorch versions without weights_only arg
            checkpoint = torch.load(self.model_path, map_location=self.device)

        # Create model based on config
        model_type = self.config.get('model.type', 'base')
        if model_type == 'small':
            self.model = MWLT_Vp_Small()
        elif model_type == 'base':
            self.model = MWLT_Vp_Base()
        elif model_type == 'large':
            self.model = MWLT_Vp_Large()

        self.model.load_state_dict(checkpoint['model_state_dict'])
        self.model = self.model.to(self.device)
        self.model.eval()

        self.logger.info("Model loaded successfully")
        return checkpoint.get('config', {})

    def perform_cross_validation(self) -> Dict[str, Any]:
        """Perform comprehensive cross-validation"""
        self.logger.info("Starting cross-validation analysis...")

        # Prepare data
        input_data, target_data = self.data_processor.create_training_data()
        if len(input_data) == 0:
            raise ValueError("No data available for cross-validation")

        n_folds = self.config.get('validation.cv_folds', 5)
        kfold = KFold(n_splits=n_folds, shuffle=True, random_state=42)

        cv_results = []
        fold_predictions = []
        fold_actuals = []

        self.logger.info(f"Performing {n_folds}-fold cross-validation...")

        for fold_idx, (train_idx, val_idx) in enumerate(kfold.split(input_data)):
            self.logger.info(f"Processing fold {fold_idx + 1}/{n_folds}")

            # Split data for this fold
            fold_train_inputs = input_data[train_idx]
            fold_train_targets = target_data[train_idx]
            fold_val_inputs = input_data[val_idx]
            fold_val_targets = target_data[val_idx]

            # Create datasets
            val_dataset = VpDataset(fold_val_inputs, fold_val_targets, self.normalizer, self.config, transform=False)
            val_loader = DataLoader(val_dataset, batch_size=1, shuffle=False)

            # Evaluate on this fold
            fold_metrics, predictions, actuals = self._evaluate_fold(val_loader)
            cv_results.append(fold_metrics)
            fold_predictions.extend(predictions)
            fold_actuals.extend(actuals)

        # Calculate overall CV metrics
        cv_summary = self._calculate_cv_summary(cv_results)

        # Save CV results
        self._save_cv_results(cv_results, cv_summary)

        return {
            'cv_results': cv_results,
            'cv_summary': cv_summary,
            'all_predictions': fold_predictions,
            'all_actuals': fold_actuals
        }

    def _evaluate_fold(self, val_loader: DataLoader) -> Tuple[Dict[str, float], List[float], List[float]]:
        """Evaluate model on a single fold"""
        predictions = []
        actuals = []

        with torch.no_grad():
            for inputs, targets in val_loader:
                inputs, targets = inputs.to(self.device), targets.to(self.device)
                outputs = self.model(inputs)

                predictions.extend(outputs.cpu().numpy().flatten())
                actuals.extend(targets.cpu().numpy().flatten())

        predictions = np.array(predictions)
        actuals = np.array(actuals)

        metrics = {
            'r2': r2_score(actuals, predictions),
            'rmse': np.sqrt(mean_squared_error(actuals, predictions)),
            'mae': mean_absolute_error(actuals, predictions),
            'mse': mean_squared_error(actuals, predictions)
        }

        return metrics, predictions.tolist(), actuals.tolist()

    def _calculate_cv_summary(self, cv_results: List[Dict[str, float]]) -> Dict[str, Any]:
        """Calculate cross-validation summary statistics"""
        metrics = ['r2', 'rmse', 'mae', 'mse']
        summary = {}

        for metric in metrics:
            values = [result[metric] for result in cv_results]
            summary[metric] = {
                'mean': np.mean(values),
                'std': np.std(values),
                'min': np.min(values),
                'max': np.max(values),
                'values': values
            }

            # Calculate confidence interval
            confidence_level = self.config.get('validation.confidence_level', 0.95)
            alpha = 1 - confidence_level
            ci = stats.t.interval(confidence_level, len(values)-1,
                                loc=np.mean(values), scale=stats.sem(values))
            summary[metric]['confidence_interval'] = ci

        return summary

    def _save_cv_results(self, cv_results: List[Dict[str, float]], cv_summary: Dict[str, Any]):
        """Save cross-validation results"""
        # Save detailed results
        results_file = self.validation_dir / 'cv_results.json'
        with open(results_file, 'w') as f:
            json.dump({
                'cv_results': cv_results,
                'cv_summary': cv_summary
            }, f, indent=2)

        # Create CV visualization
        plot_vp.create_cv_plots(
            cv_results,
            cv_summary,
            self.validation_dir,
            self.config.get('output.interactive_plots', False)
        )

        self.logger.info(f"Cross-validation results saved to {self.validation_dir}")

    def generate_validation_report(self, cv_results: Dict[str, Any]) -> Dict[str, Any]:
        """Generate comprehensive validation report"""
        self.logger.info("Generating validation report...")

        report = {
            'model_path': self.model_path,
            'validation_timestamp': datetime.now().isoformat(),
            'cross_validation': cv_results['cv_summary'],
            'model_performance': self._assess_model_performance(cv_results['cv_summary']),
            'recommendations': self._generate_recommendations(cv_results['cv_summary'])
        }

        # Save report
        report_file = self.validation_dir / 'validation_report.json'
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2)

        self.logger.info(f"Validation report saved to {report_file}")
        return report

    def _assess_model_performance(self, cv_summary: Dict[str, Any]) -> Dict[str, str]:
        """Assess model performance based on CV results"""
        r2_mean = cv_summary['r2']['mean']
        r2_std = cv_summary['r2']['std']

        # Performance assessment
        if r2_mean >= 0.8:
            performance = 'Excellent'
        elif r2_mean >= 0.6:
            performance = 'Good'
        elif r2_mean >= 0.4:
            performance = 'Fair'
        else:
            performance = 'Poor'

        # Consistency assessment
        if r2_std <= 0.05:
            consistency = 'High'
        elif r2_std <= 0.1:
            consistency = 'Medium'
        else:
            consistency = 'Low'

        return {
            'overall_performance': performance,
            'consistency': consistency,
            'mean_r2': f"{r2_mean:.4f}",
            'r2_std': f"{r2_std:.4f}"
        }

    def _generate_recommendations(self, cv_summary: Dict[str, Any]) -> List[str]:
        """Generate recommendations based on validation results"""
        recommendations = []

        r2_mean = cv_summary['r2']['mean']
        r2_std = cv_summary['r2']['std']

        if r2_mean < 0.6:
            recommendations.append("Consider increasing model complexity or training data")
            recommendations.append("Review data quality and preprocessing steps")

        if r2_std > 0.1:
            recommendations.append("High variance detected - consider regularization techniques")
            recommendations.append("Increase cross-validation folds for more stable estimates")

        if r2_mean >= 0.8 and r2_std <= 0.05:
            recommendations.append("Model shows excellent and consistent performance")
            recommendations.append("Ready for production deployment")

        return recommendations


# ============================================================================
# STAGE 3: VP PREDICTION MANAGER
# ============================================================================

class VpPredictionManager:
    """Comprehensive prediction manager for VP models"""

    def __init__(self, model_path: str, config: VpPipelineConfig, output_dir: Path):
        """Initialize prediction manager"""
        self.model_path = model_path
        self.config = config
        self.output_dir = output_dir
        self.prediction_dir = output_dir / 'prediction'
        self.prediction_dir.mkdir(parents=True, exist_ok=True)

        self.logger = self._setup_logger()
        self.data_processor = VpDataProcessor(config)
        self.device = get_device(config.get('training.device', 0))

        self.model = None
        self.normalizer = VpDataNormalizer()

    def _setup_logger(self) -> logging.Logger:
        """Setup logger for prediction"""
        logger = logging.getLogger('VpPredictionManager')
        logger.setLevel(logging.INFO)
        if not logger.handlers:
            # Ensure prediction directory exists before creating log file
            self.prediction_dir.mkdir(parents=True, exist_ok=True)

            # File handler
            log_file = self.prediction_dir / 'prediction.log'
            file_handler = logging.FileHandler(log_file, encoding='utf-8')
            file_formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            file_handler.setFormatter(file_formatter)
            logger.addHandler(file_handler)

            # Console handler
            console_handler = logging.StreamHandler()
            console_formatter = logging.Formatter('%(levelname)s - %(message)s')
            console_handler.setFormatter(console_formatter)
            logger.addHandler(console_handler)
        return logger

    def load_model_for_inference(self):
        """Load trained model for inference"""
        self.logger.info(f"Loading model for inference from {self.model_path}")

        if not Path(self.model_path).exists():
            raise FileNotFoundError(f"Model file not found: {self.model_path}")

        # Load checkpoint
        try:
            # PyTorch 2.6+: default weights_only=True can break loading older checkpoints.
            # Explicitly set weights_only=False for trusted local checkpoints.
            checkpoint = torch.load(self.model_path, map_location=self.device, weights_only=False)
        except TypeError:
            # Older PyTorch versions without weights_only arg
            checkpoint = torch.load(self.model_path, map_location=self.device)

        # Create model
        model_type = self.config.get('model.type', 'base')
        if model_type == 'small':
            self.model = MWLT_Vp_Small()
        elif model_type == 'base':
            self.model = MWLT_Vp_Base()
        elif model_type == 'large':
            self.model = MWLT_Vp_Large()

        self.model.load_state_dict(checkpoint['model_state_dict'])
        self.model = self.model.to(self.device)
        self.model.eval()

        self.logger.info("Model loaded successfully for inference")
        return checkpoint.get('config', {})

    def prepare_test_data(self) -> DataLoader:
        """Prepare test data for prediction"""
        self.logger.info("Preparing test data...")

        # Create test data (using same data processing as training)
        input_data, target_data = self.data_processor.create_training_data()

        if len(input_data) == 0:
            raise ValueError("No test data available")

        # Use a subset for demonstration (last 20% as test set)
        test_size = int(0.2 * len(input_data))
        test_inputs = input_data[-test_size:]
        test_targets = target_data[-test_size:]

        self.logger.info(f"Test samples: {len(test_inputs)}")

        # Create test dataset
        test_dataset = VpDataset(test_inputs, test_targets, self.normalizer, self.config, transform=False)
        test_loader = DataLoader(test_dataset, batch_size=1, shuffle=False)

        return test_loader

    def make_predictions(self, test_loader: DataLoader) -> Dict[str, Any]:
        """Make predictions on test data"""
        self.logger.info("Making predictions on test data...")

        predictions = []
        actuals = []
        prediction_details = []

        with torch.no_grad():
            for idx, (inputs, targets) in enumerate(tqdm(test_loader, desc="Predicting")):
                inputs, targets = inputs.to(self.device), targets.to(self.device)
                outputs = self.model(inputs)

                # Store predictions and actuals
                pred_values = outputs.cpu().numpy().flatten()
                actual_values = targets.cpu().numpy().flatten()

                predictions.extend(pred_values)
                actuals.extend(actual_values)

                # Store detailed information for visualization
                prediction_details.append({
                    'sample_idx': idx,
                    'predictions': pred_values.tolist(),
                    'actuals': actual_values.tolist(),
                    'inputs': inputs.cpu().numpy().tolist()
                })

        predictions = np.array(predictions)
        actuals = np.array(actuals)

        # Calculate metrics
        metrics = {
            'r2': r2_score(actuals, predictions),
            'rmse': np.sqrt(mean_squared_error(actuals, predictions)),
            'mae': mean_absolute_error(actuals, predictions),
            'mse': mean_squared_error(actuals, predictions)
        }

        self.logger.info(f"Prediction metrics - R²: {metrics['r2']:.4f}, RMSE: {metrics['rmse']:.2f}")

        return {
            'predictions': predictions,
            'actuals': actuals,
            'metrics': metrics,
            'prediction_details': prediction_details
        }

    def visualize_results(self, prediction_results: Dict[str, Any]):
        """Create comprehensive prediction visualizations"""
        self.logger.info("Creating prediction visualizations...")

        # Create prediction visualizations using the new plotting module
        plot_vp.visualize_prediction_results(
            prediction_results,
            self.prediction_dir,
            self.config.get('output.interactive_plots', False)
        )

        self.logger.info(f"Prediction visualizations saved to {self.prediction_dir}")

    def generate_prediction_report(self, prediction_results: Dict[str, Any]) -> Dict[str, Any]:
        """Generate comprehensive prediction report"""
        self.logger.info("Generating prediction report...")

        metrics = prediction_results['metrics']

        report = {
            'model_path': self.model_path,
            'prediction_timestamp': datetime.now().isoformat(),
            'test_samples': len(prediction_results['predictions']),
            'performance_metrics': metrics,
            'performance_assessment': self._assess_prediction_performance(metrics),
            'statistical_analysis': self._perform_statistical_analysis(prediction_results)
        }

        # Save report
        report_file = self.prediction_dir / 'prediction_report.json'
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2)

        # Save detailed predictions
        predictions_file = self.prediction_dir / 'detailed_predictions.json'
        with open(predictions_file, 'w') as f:
            json.dump({
                'predictions': prediction_results['predictions'].tolist(),
                'actuals': prediction_results['actuals'].tolist(),
                'metrics': metrics
            }, f, indent=2)

        self.logger.info(f"Prediction report saved to {report_file}")
        return report

    def _assess_prediction_performance(self, metrics: Dict[str, float]) -> Dict[str, str]:
        """Assess prediction performance"""
        r2 = metrics['r2']

        if r2 >= 0.8:
            performance = 'Excellent'
            recommendation = 'Model ready for production use'
        elif r2 >= 0.6:
            performance = 'Good'
            recommendation = 'Model suitable for most applications'
        elif r2 >= 0.4:
            performance = 'Fair'
            recommendation = 'Consider model improvements'
        else:
            performance = 'Poor'
            recommendation = 'Model requires significant improvements'

        return {
            'overall_performance': performance,
            'recommendation': recommendation,
            'r2_score': f"{r2:.4f}",
            'rmse': f"{metrics['rmse']:.2f}"
        }

    def _perform_statistical_analysis(self, prediction_results: Dict[str, Any]) -> Dict[str, Any]:
        """Perform statistical analysis of predictions"""
        predictions = prediction_results['predictions']
        actuals = prediction_results['actuals']
        residuals = predictions - actuals

        # Statistical tests
        from scipy.stats import shapiro, jarque_bera

        # Normality test for residuals
        shapiro_stat, shapiro_p = shapiro(residuals[:5000] if len(residuals) > 5000 else residuals)
        jb_stat, jb_p = jarque_bera(residuals)

        return {
            'residuals_stats': {
                'mean': float(np.mean(residuals)),
                'std': float(np.std(residuals)),
                'min': float(np.min(residuals)),
                'max': float(np.max(residuals)),
                'median': float(np.median(residuals))
            },
            'normality_tests': {
                'shapiro_wilk': {'statistic': float(shapiro_stat), 'p_value': float(shapiro_p)},
                'jarque_bera': {'statistic': float(jb_stat), 'p_value': float(jb_p)}
            },
            'prediction_range': {
                'min': float(np.min(predictions)),
                'max': float(np.max(predictions)),
                'mean': float(np.mean(predictions)),
                'std': float(np.std(predictions))
            },
            'actual_range': {
                'min': float(np.min(actuals)),
                'max': float(np.max(actuals)),
                'mean': float(np.mean(actuals)),
                'std': float(np.std(actuals))
            }
        }


# ============================================================================
# MAIN VP PREDICTION PIPELINE
# ============================================================================

class VpPredictionPipeline:
    """Main pipeline orchestrator for comprehensive VP prediction workflow"""

    def __init__(self, config_path: Optional[str] = None, output_dir: Optional[str] = None):
        """Initialize the VP prediction pipeline"""
        # Load configuration
        if config_path and Path(config_path).exists():
            self.config = VpPipelineConfig.load(config_path)
        else:
            self.config = VpPipelineConfig()

        # Setup output directory
        if output_dir:
            self.output_dir = Path(output_dir)
        else:
            self.output_dir = Path(self.config.get('output.base_dir', 'vp_prediction_outputs'))

        self.output_dir.mkdir(parents=True, exist_ok=True)

        # Setup main logger
        self.logger = self._setup_main_logger()

        # Initialize managers
        self.training_manager = None
        self.validation_manager = None
        self.prediction_manager = None

        self.logger.info(f"VP Prediction Pipeline initialized")
        self.logger.info(f"Output directory: {self.output_dir.absolute()}")
        self.logger.info(f"Using vp_predictor package: {USING_VP_PREDICTOR_PACKAGE}")

    def _setup_main_logger(self) -> logging.Logger:
        """Setup main pipeline logger"""
        logger = logging.getLogger('VpPredictionPipeline')
        logger.setLevel(logging.INFO)

        if not logger.handlers:
            # File handler
            log_file = self.output_dir / 'pipeline.log'
            file_handler = logging.FileHandler(log_file, encoding='utf-8')
            file_formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            file_handler.setFormatter(file_formatter)
            logger.addHandler(file_handler)

            # Console handler
            console_handler = logging.StreamHandler()
            console_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
            console_handler.setFormatter(console_formatter)
            logger.addHandler(console_handler)

        return logger

    def _convert_to_json_serializable(self, obj):
        """Convert numpy types to JSON serializable Python types"""
        if isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, dict):
            return {key: self._convert_to_json_serializable(value) for key, value in obj.items()}
        elif isinstance(obj, list):
            return [self._convert_to_json_serializable(item) for item in obj]
        else:
            return obj

    def setup_directories(self):
        """Setup organized output directory structure"""
        directories = ['training', 'validation', 'prediction']

        for dir_name in directories:
            dir_path = self.output_dir / dir_name
            dir_path.mkdir(parents=True, exist_ok=True)
            self.logger.info(f"Created directory: {dir_path}")

        # Save configuration
        config_file = self.output_dir / 'pipeline_config.json'
        self.config.save(str(config_file))
        self.logger.info(f"Configuration saved to: {config_file}")

    def run_complete_pipeline(self) -> Dict[str, Any]:
        """Execute the complete three-stage VP prediction pipeline"""
        self.logger.info("="*80)
        self.logger.info("STARTING COMPREHENSIVE VP PREDICTION PIPELINE")
        self.logger.info("="*80)

        pipeline_start_time = time.time()
        results = {}

        try:
            # Setup directories
            self.setup_directories()

            # Stage 1: Training
            self.logger.info("\n" + "="*60)
            self.logger.info("STAGE 1: VP MODEL TRAINING")
            self.logger.info("="*60)
            training_results = self.run_training_stage()
            results['training'] = training_results

            # Stage 2: Validation
            self.logger.info("\n" + "="*60)
            self.logger.info("STAGE 2: VP MODEL VALIDATION")
            self.logger.info("="*60)
            validation_results = self.run_validation_stage(training_results['best_model_path'])
            results['validation'] = validation_results

            # Stage 3: Prediction
            self.logger.info("\n" + "="*60)
            self.logger.info("STAGE 3: VP MODEL PREDICTION")
            self.logger.info("="*60)
            prediction_results = self.run_prediction_stage(training_results['best_model_path'])
            results['prediction'] = prediction_results

            # Generate final summary
            pipeline_time = time.time() - pipeline_start_time
            summary = self._generate_pipeline_summary(results, pipeline_time)
            results['pipeline_summary'] = summary

            self.logger.info("\n" + "="*80)
            self.logger.info("VP PREDICTION PIPELINE COMPLETED SUCCESSFULLY")
            self.logger.info("="*80)
            self.logger.info(f"Total execution time: {pipeline_time:.2f} seconds")
            self.logger.info(f"Results saved to: {self.output_dir.absolute()}")

            return results

        except Exception as e:
            self.logger.error(f"Pipeline failed with error: {str(e)}")
            import traceback
            self.logger.error(traceback.format_exc())
            raise

    def run_training_stage(self) -> Dict[str, Any]:
        """Execute Stage 1: VP Model Training"""
        self.training_manager = VpTrainingManager(self.config, self.output_dir)

        # Prepare data
        train_loader, val_loader = self.training_manager.prepare_data()

        # Create and setup model
        self.training_manager.create_model()
        self.training_manager.setup_training()

        # Train model
        training_results = self.training_manager.train_model(train_loader, val_loader)

        # Save results
        self.training_manager.save_training_results(training_results)

        self.logger.info(f"✅ Training stage completed successfully")
        self.logger.info(f"   Best R²: {training_results['final_metrics']['best_val_r2']:.4f}")
        self.logger.info(f"   Best RMSE: {training_results['final_metrics']['best_val_rmse']:.2f}")

        return training_results

    def run_validation_stage(self, model_path: str) -> Dict[str, Any]:
        """Execute Stage 2: VP Model Validation"""
        self.validation_manager = VpValidationManager(model_path, self.config, self.output_dir)

        # Load trained model
        self.validation_manager.load_trained_model()

        # Perform cross-validation
        cv_results = self.validation_manager.perform_cross_validation()

        # Generate validation report
        validation_report = self.validation_manager.generate_validation_report(cv_results)

        self.logger.info(f"✅ Validation stage completed successfully")
        self.logger.info(f"   Mean CV R²: {cv_results['cv_summary']['r2']['mean']:.4f} ± {cv_results['cv_summary']['r2']['std']:.4f}")
        self.logger.info(f"   Performance: {validation_report['model_performance']['overall_performance']}")

        return {
            'cv_results': cv_results,
            'validation_report': validation_report
        }

    def run_prediction_stage(self, model_path: str) -> Dict[str, Any]:
        """Execute Stage 3: VP Model Prediction"""
        self.prediction_manager = VpPredictionManager(model_path, self.config, self.output_dir)

        # Load model for inference
        self.prediction_manager.load_model_for_inference()

        # Prepare test data
        test_loader = self.prediction_manager.prepare_test_data()

        # Make predictions
        prediction_results = self.prediction_manager.make_predictions(test_loader)

        # Create visualizations
        self.prediction_manager.visualize_results(prediction_results)

        # Generate prediction report
        prediction_report = self.prediction_manager.generate_prediction_report(prediction_results)

        self.logger.info(f"✅ Prediction stage completed successfully")
        self.logger.info(f"   Test R²: {prediction_results['metrics']['r2']:.4f}")
        self.logger.info(f"   Test RMSE: {prediction_results['metrics']['rmse']:.2f}")

        return {
            'prediction_results': prediction_results,
            'prediction_report': prediction_report
        }

    def _generate_pipeline_summary(self, results: Dict[str, Any], execution_time: float) -> Dict[str, Any]:
        """Generate comprehensive pipeline summary"""
        summary = {
            'pipeline_execution_time': execution_time,
            'timestamp': datetime.now().isoformat(),
            'configuration': self.config.config,
            'stages_completed': list(results.keys()),
            'performance_summary': {
                'training': {
                    'best_val_r2': results['training']['final_metrics']['best_val_r2'],
                    'best_val_rmse': results['training']['final_metrics']['best_val_rmse']
                },
                'validation': {
                    'mean_cv_r2': results['validation']['cv_results']['cv_summary']['r2']['mean'],
                    'cv_r2_std': results['validation']['cv_results']['cv_summary']['r2']['std'],
                    'performance_rating': results['validation']['validation_report']['model_performance']['overall_performance']
                },
                'prediction': {
                    'test_r2': results['prediction']['prediction_results']['metrics']['r2'],
                    'test_rmse': results['prediction']['prediction_results']['metrics']['rmse'],
                    'test_samples': len(results['prediction']['prediction_results']['predictions'])
                }
            },
            'output_files': {
                'training_model': results['training']['best_model_path'],
                'validation_report': str(self.output_dir / 'validation' / 'validation_report.json'),
                'prediction_report': str(self.output_dir / 'prediction' / 'prediction_report.json'),
                'pipeline_log': str(self.output_dir / 'pipeline.log')
            }
        }

        # Save summary
        summary_file = self.output_dir / 'pipeline_summary.json'
        with open(summary_file, 'w') as f:
            json.dump(self._convert_to_json_serializable(summary), f, indent=2)

        return summary


# ============================================================================
# COMMAND LINE INTERFACE AND MAIN ENTRY POINT
# ============================================================================

def main():
    """Main entry point for the VP prediction pipeline"""
    parser = argparse.ArgumentParser(
        description="Comprehensive VP (Acoustic Velocity) Prediction Pipeline",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Run complete pipeline with default settings
  python train_vp_improved.py

  # Run complete pipeline with custom config
  python train_vp_improved.py --config custom_config.json

  # Run complete pipeline with custom output directory
  python train_vp_improved.py --output-dir custom_outputs

  # Run only training stage
  python train_vp_improved.py --stage training

  # Run only validation stage (requires trained model)
  python train_vp_improved.py --stage validation --model-path path/to/model.pth

  # Run only prediction stage (requires trained model)
  python train_vp_improved.py --stage prediction --model-path path/to/model.pth

  # Run with interactive plots displayed alongside saving
  python train_vp_improved.py --interactive-plots
        """
    )

    parser.add_argument('--config', type=str, help='Path to configuration JSON file')
    parser.add_argument('--output-dir', type=str, help='Output directory for results')
    parser.add_argument('--stage', choices=['training', 'validation', 'prediction', 'all'],
                       default='all', help='Pipeline stage to run')
    parser.add_argument('--model-path', type=str, help='Path to trained model (for validation/prediction stages)')
    parser.add_argument('--interactive-plots', action='store_true', help='Display interactive plots using matplotlib after saving')
    parser.add_argument('--verbose', action='store_true', help='Enable verbose logging')

    args = parser.parse_args()

    # Setup logging level
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    try:
        # Initialize pipeline
        pipeline = VpPredictionPipeline(config_path=args.config, output_dir=args.output_dir)

        # Update config with command-line arguments
        if args.interactive_plots:
            pipeline.config.config['output']['interactive_plots'] = True
            print("✅ Interactive plots enabled via command line argument")
            # Set up interactive matplotlib backend
            try:
                matplotlib.use('TkAgg')  # Try TkAgg first
                print("✅ Using TkAgg backend for interactive plots")
            except ImportError:
                try:
                    matplotlib.use('Qt5Agg')  # Try Qt5Agg as fallback
                    print("✅ Using Qt5Agg backend for interactive plots")
                except ImportError:
                    try:
                        matplotlib.use('Qt4Agg')  # Try Qt4Agg as fallback
                        print("✅ Using Qt4Agg backend for interactive plots")
                    except ImportError:
                        print("⚠️  No interactive backend available, plots will be saved but not displayed")
            plt.ion()  # Turn on interactive mode
            # Test if interactive backend is working
            try:
                fig = plt.figure()
                plt.close(fig)  # Close test figure
                print("✅ Interactive plotting test successful")
            except Exception as e:
                print(f"⚠️  Interactive plotting test failed: {e}")
                print("⚠️  Plots will be saved but may not display interactively")
        else:
            print("ℹ️  Interactive plots disabled (use --interactive-plots to enable)")

        if args.stage == 'all':
            # Run complete pipeline
            results = pipeline.run_complete_pipeline()
            print(f"\n🎉 Complete pipeline executed successfully!")
            print(f"📊 Final Performance Summary:")
            print(f"   Training Best R²: {results['training']['final_metrics']['best_val_r2']:.4f}")
            print(f"   Validation Mean R²: {results['validation']['cv_results']['cv_summary']['r2']['mean']:.4f}")
            print(f"   Test R²: {results['prediction']['prediction_results']['metrics']['r2']:.4f}")
            print(f"📁 Results saved to: {pipeline.output_dir.absolute()}")

        elif args.stage == 'training':
            # Run only training stage
            pipeline.setup_directories()
            results = pipeline.run_training_stage()
            print(f"\n🎉 Training stage completed successfully!")
            print(f"📊 Best Validation R²: {results['final_metrics']['best_val_r2']:.4f}")
            print(f"📁 Model saved to: {results['best_model_path']}")

        elif args.stage == 'validation':
            # Run only validation stage
            if not args.model_path:
                raise ValueError("--model-path is required for validation stage")
            pipeline.setup_directories()
            results = pipeline.run_validation_stage(args.model_path)
            print(f"\n🎉 Validation stage completed successfully!")
            print(f"📊 Mean CV R²: {results['cv_results']['cv_summary']['r2']['mean']:.4f}")
            print(f"📊 Performance: {results['validation_report']['model_performance']['overall_performance']}")

        elif args.stage == 'prediction':
            # Run only prediction stage
            if not args.model_path:
                raise ValueError("--model-path is required for prediction stage")
            pipeline.setup_directories()
            results = pipeline.run_prediction_stage(args.model_path)
            print(f"\n🎉 Prediction stage completed successfully!")
            print(f"📊 Test R²: {results['prediction_results']['metrics']['r2']:.4f}")
            print(f"📊 Test RMSE: {results['prediction_results']['metrics']['rmse']:.2f}")

    except Exception as e:
        print(f"\n❌ Pipeline failed with error: {str(e)}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
