"""
General Well Log Transformer - Multi-Curve Prediction Package

This package provides both VpTransformer (backward compatible) and GeneralWellLogTransformer
for flexible multi-curve well log predictions with proper scaling and enhanced architecture.

Core Components:
- GeneralWellLogTransformer: Main transformer for arbitrary curve combinations
- VpTransformer: Backward compatible Vp-specific transformer
- GeneralDataNormalizer: Multi-curve normalization with curve-specific methods
- VpPredictor: Backward compatible prediction wrapper
- GeneralWellLogPredictor: New general-purpose prediction API

Key Features (2025 Refactoring):
- Multi-curve prediction support with configurable input/output combinations
- Backward compatibility maintained for existing VpTransformer code
- Configuration-driven architecture with predefined templates
- Enhanced normalization with curve-specific preprocessing
- Improved decoder without artificial sigmoid constraints
- Template-based model creation for common use cases
"""

from .vp_model_improved import (
    VpTransformer,
    VpDecoder,
    MWLT_Vp_Small,
    MWLT_Vp_Base, 
    MWLT_Vp_Large,
    VpDataNormalizer,
    VpLoss,
    VpDataset,
    create_improved_vp_data,
    find_data_files
)

from .model import (
    Input_Embedding,
    ResCNN,
    PositionalEncoding,
    TransformerEncoder,
    TransformerBlock,
    SelfAttention,
    FeedForward,
    Decoder,
    MWL_Transformer,
    MWLT_Small,
    MWLT_Base,
    MWLT_Large
)

from .utils import (
    get_device,
    save_checkpoint,
    load_checkpoint,
    EarlyStopping,
    cal_RMSE,
    cal_R2
)

from .las_processor import (
    LASProcessor,
    create_test_las_file
)

# Import the integration wrappers
try:
    from .predictor import VpPredictor, VpTransformerAPI
except ImportError:
    # predictor.py not yet created - use new API
    pass

# Import new general transformer components (Phase 1 + Phase 2)
from .core import (
    # Phase 1 - Core architecture
    GeneralWellLogTransformer,
    GeneralDecoder, GeneralDataNormalizer,
    GWLT_VpPrediction, GWLT_DensityPrediction, GWLT_MultiCurve,
    GWLT_MissingSectionFill, GWLT_FastInference,
    GWLT_Small, GWLT_Base, GWLT_Large,
    GWLT_Vp_Small, GWLT_Vp_Base, GWLT_Vp_Large,
    VpTransformer_Compatible,
    
    # Phase 2 - Training infrastructure
    GeneralWellLogDataset, VpDatasetCompatible, create_dataset_from_template,
    GeneralWellLogLoss, CurveSpecificLossFactory, VpLossCompatible,
    create_vp_loss, create_density_loss, create_neutron_loss,
    create_gamma_ray_loss, create_resistivity_loss,
    GeneralTrainingManager, create_vp_trainer, create_density_trainer
)

# Import configuration system (Phase 1 + Phase 2)
from .configs import (
    # Phase 1 - Core configurations
    CURVE_CONFIGURATIONS, MODEL_TEMPLATES,
    get_curve_config, get_model_template,
    validate_curve_config, validate_model_config,
    ConfigurationError,
    
    # Phase 2 - Training configurations
    TRAINING_TEMPLATES, DATA_TEMPLATES, INTEGRATED_TEMPLATES,
    get_training_template, get_data_template, get_integrated_template,
    create_custom_training_config, get_curve_training_hints,
    validate_training_config, list_available_templates
)

# Import new API interfaces
from .api import (
    GeneralWellLogPredictor,
    VpPredictor as VpPredictor_New,  # New implementation
    VpTransformerAPI as VpTransformerAPI_New  # New implementation
)

# Package metadata
__version__ = "1.0.0"
__author__ = "VpTransformer Team"
__description__ = "Improved VpTransformer for sonic velocity prediction from well logs"

# Legacy model configuration (backward compatibility)
MODEL_CONFIG = {
    'input_curves': ['GR', 'CNL', 'DEN', 'RLLD'],
    'output_curve': 'VP',
    'sequence_length': 640,
    'model_variants': {
        'small': {'res_blocks': 2, 'encoders': 2, 'heads': 2, 'features': 64},
        'base': {'res_blocks': 4, 'encoders': 4, 'heads': 4, 'features': 64},
        'large': {'res_blocks': 6, 'encoders': 6, 'heads': 8, 'features': 128}
    },
    'vp_range': (40, 400),  # μs/ft - proper scaling without artificial bounds
    'device_preference': 'auto',  # 'auto', 'gpu', 'cpu'
    'improvements': {
        'sigmoid_fix': True,           # Fixed sigmoid activation issue
        'proper_normalization': True,  # Enhanced normalization approach
        'data_leakage_prevented': True, # Verified training/testing independence
        'enhanced_decoder': True       # Improved VpDecoder architecture
    },
    'training_params': {
        'learning_rate': 1e-4,
        'batch_size': 8,
        'epochs': 200,
        'patience': 50
    }
}

# New general configuration access
def get_general_config(template_name='vp_prediction'):
    """Get general configuration from templates (recommended)"""
    return get_model_template(template_name)

# Essential dependencies
REQUIRED_DEPENDENCIES = [
    'torch>=1.8.0',
    'numpy>=1.19.0', 
    'h5py>=3.1.0',
    'scikit-learn>=0.24.0'
]

# Optional dependencies for enhanced features
OPTIONAL_DEPENDENCIES = [
    'matplotlib>=3.3.0',  # Plotting and visualization
    'pandas>=1.2.0',      # Data analysis
    'lasio>=0.30',        # LAS file reading
    'thop>=0.0.31'        # Model profiling
]

# Quick access functions (backward compatible)
def get_model(model_type='base', **kwargs):
    """
    Get a pre-configured VpTransformer model (backward compatible)
    
    Args:
        model_type: 'small', 'base', or 'large'
        **kwargs: Additional model parameters
        
    Returns:
        VpTransformer model
    """
    if model_type == 'small':
        return MWLT_Vp_Small(**kwargs)
    elif model_type == 'base':
        return MWLT_Vp_Base(**kwargs)
    elif model_type == 'large':
        return MWLT_Vp_Large(**kwargs)
    else:
        raise ValueError(f"Unknown model type: {model_type}")

# New general model access functions
def get_general_model(template_name='vp_prediction', **kwargs):
    """
    Get a GeneralWellLogTransformer from template
    
    Args:
        template_name: Template name ('vp_prediction', 'multi_curve_basic', etc.)
        **kwargs: Override parameters
        
    Returns:
        GeneralWellLogTransformer model
    """
    return GeneralWellLogTransformer.from_template(template_name, **kwargs)

def create_custom_model(input_curves, output_curves, model_size='base', **kwargs):
    """
    Create custom model for specific curve combination
    
    Args:
        input_curves: List of input curve names
        output_curves: List of output curve names
        model_size: 'small', 'base', or 'large'
        **kwargs: Additional parameters
        
    Returns:
        GeneralWellLogTransformer model
    """
    if model_size == 'small':
        return GWLT_Small(input_curves=input_curves, output_curves=output_curves, **kwargs)
    elif model_size == 'base':
        return GWLT_Base(input_curves=input_curves, output_curves=output_curves, **kwargs)
    elif model_size == 'large':
        return GWLT_Large(input_curves=input_curves, output_curves=output_curves, **kwargs)
    else:
        raise ValueError(f"Unknown model size: {model_size}")

def get_normalizer():
    """Get a VpDataNormalizer instance (backward compatible)"""
    return VpDataNormalizer()

def get_general_normalizer(input_curves=None, output_curves=None):
    """Get a GeneralDataNormalizer instance"""
    return GeneralDataNormalizer(input_curves=input_curves, output_curves=output_curves)

def get_processor():
    """Get a LASProcessor instance"""
    return LASProcessor()

# Package information
def get_package_info():
    """Return comprehensive package information"""
    return {
        'version': __version__,
        'description': __description__,
        'author': __author__,
        'improvements': MODEL_CONFIG['improvements'],
        'model_variants': MODEL_CONFIG['model_variants'],
        'vp_range': MODEL_CONFIG['vp_range'],
        'required_deps': REQUIRED_DEPENDENCIES,
        'optional_deps': OPTIONAL_DEPENDENCIES
    }

# Compatibility aliases
VpModel = VpTransformer  # Alias for backward compatibility
VpNormalizer = VpDataNormalizer  # Shorter alias

__all__ = [
    # ===== BACKWARD COMPATIBLE EXPORTS (Original VpTransformer) =====
    # Core models (original)
    'VpTransformer', 'VpDecoder', 'VpModel',
    'MWLT_Vp_Small', 'MWLT_Vp_Base', 'MWLT_Vp_Large',
    
    # Base components (original)
    'Input_Embedding', 'ResCNN', 'PositionalEncoding',
    'TransformerEncoder', 'TransformerBlock', 'SelfAttention',
    'FeedForward', 'Decoder', 'MWL_Transformer',
    'MWLT_Small', 'MWLT_Base', 'MWLT_Large',
    
    # Data processing (original)
    'VpDataNormalizer', 'VpNormalizer', 'VpLoss', 'VpDataset',
    'LASProcessor', 'create_test_las_file',
    
    # Utilities (original)
    'get_device', 'save_checkpoint', 'load_checkpoint',
    'EarlyStopping', 'cal_RMSE', 'cal_R2',
    
    # Data utilities (original)
    'create_improved_vp_data', 'find_data_files',
    
    # Quick access functions (original)
    'get_model', 'get_normalizer', 'get_processor', 'get_package_info',
    
    # ===== NEW GENERAL TRANSFORMER EXPORTS =====
    # Phase 1 - Core general components
    'GeneralWellLogTransformer', 'GeneralDecoder', 'GeneralDataNormalizer',
    
    # Template-based models
    'GWLT_VpPrediction', 'GWLT_DensityPrediction', 'GWLT_MultiCurve',
    'GWLT_MissingSectionFill', 'GWLT_FastInference',
    
    # Size variants (general)
    'GWLT_Small', 'GWLT_Base', 'GWLT_Large',
    
    # Backward compatible general variants
    'GWLT_Vp_Small', 'GWLT_Vp_Base', 'GWLT_Vp_Large', 'VpTransformer_Compatible',
    
    # Phase 2 - Training infrastructure
    # Datasets
    'GeneralWellLogDataset', 'VpDatasetCompatible', 'create_dataset_from_template',
    
    # Loss functions
    'GeneralWellLogLoss', 'CurveSpecificLossFactory', 'VpLossCompatible',
    'create_vp_loss', 'create_density_loss', 'create_neutron_loss',
    'create_gamma_ray_loss', 'create_resistivity_loss',
    
    # Training managers
    'GeneralTrainingManager', 'create_vp_trainer', 'create_density_trainer',
    
    # Configuration system
    'CURVE_CONFIGURATIONS', 'MODEL_TEMPLATES',
    'get_curve_config', 'get_model_template', 'get_general_config',
    'validate_curve_config', 'validate_model_config', 'ConfigurationError',
    
    # Training configurations (Phase 2)
    'TRAINING_TEMPLATES', 'DATA_TEMPLATES', 'INTEGRATED_TEMPLATES',
    'get_training_template', 'get_data_template', 'get_integrated_template',
    'create_custom_training_config', 'get_curve_training_hints',
    'validate_training_config', 'list_available_templates',
    
    # New API interfaces
    'GeneralWellLogPredictor', 'VpPredictor_New', 'VpTransformerAPI_New',
    
    # New access functions
    'get_general_model', 'create_custom_model', 'get_general_normalizer',
    
    # ===== LEGACY CONFIGURATION AND METADATA =====
    'MODEL_CONFIG', 'REQUIRED_DEPENDENCIES', 'OPTIONAL_DEPENDENCIES',
    '__version__', '__author__', '__description__'
]