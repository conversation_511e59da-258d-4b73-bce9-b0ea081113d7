# VP (Acoustic Velocity) Prediction Pipeline

## 🚀 Quick Start

**Want to train and predict VP right away?** → See **[VP_QUICK_START_GUIDE.md](VP_QUICK_START_GUIDE.md)**

## 📁 Directory Structure

```
init_vp_pred/
├── 🚀 MAIN COMPONENTS
│   ├── train_vp_improved.py          # Main VP prediction pipeline
│   ├── vp_model_improved.py          # VP model architectures
│   ├── model.py                      # Base transformer components
│   ├── utils.py                      # Utility functions
│   ├── las_processor.py              # Data processing
│   ├── dataset.py                    # Dataset utilities
│   └── test_vp_pipeline.py           # Pipeline test suite
│
├── 📦 vp_predictor/                  # Modular package architecture
│   ├── __init__.py
│   ├── vp_model_improved.py
│   ├── model.py
│   ├── utils.py
│   ├── las_processor.py
│   ├── predictor.py
│   ├── core/
│   ├── configs/
│   └── api/
│
├── 📊 vp_prediction_outputs/         # Auto-generated results
│   ├── training/
│   ├── validation/
│   └── prediction/
│
├── 📄 DATA FILES
│   ├── A1.hdf5
│   ├── A2.hdf5
│   ├── A1_converted.las
│   └── test_well.las
│
├── 📝 DOCUMENTATION
│   ├── README.md                     # Main project overview
│   ├── VP_QUICK_START_GUIDE.md       # Step-by-step usage guide
│   ├── Codebase.md                   # Detailed codebase structure
│   ├── SETUP_CHECKLIST.md            # Installation checklist
│   └── docs/                         # Documentation folder
│       └── plan/
│           └── 1a_plan_refactoring_vp_ph1.md
│
└── ⚙️ PROJECT CONFIGURATION
    ├── requirements.txt
    ├── setup.py
    └── __init__.py
```

## ⚡ One-Command Usage

```bash
# Complete VP training and prediction pipeline
python train_vp_improved.py
```

This single command will:
1. ✅ Train a VP prediction model
2. ✅ Validate the model with cross-validation
3. ✅ Make predictions and generate visualizations
4. ✅ Save all results to `vp_prediction_outputs/`

## 📊 Visualization Only

To visualize results without retraining (much faster):

```bash
# Visualize training results
python visualize_vp_results.py --type training --dir ./vp_prediction_outputs/training

# Visualize cross-validation results
python visualize_vp_results.py --type validation --dir ./vp_prediction_outputs/validation

# Visualize prediction results
python visualize_vp_results.py --type prediction --dir ./vp_prediction_outputs/prediction

# Visualize with interactive plots
python visualize_vp_results.py --type prediction --dir ./vp_prediction_outputs/prediction --interactive
```

This allows you to quickly view and analyze results without retraining the model.

## 📋 Prerequisites

1. **Install Dependencies:**
   ```bash
   pip install torch numpy matplotlib scikit-learn scipy h5py tqdm
   ```

2. **Ensure Data Files Available:**
   - `A1.hdf5` and `A2.hdf5` (pipeline will auto-detect location)

## 📦 Package Installation (Optional)

To install as a Python package for enhanced functionality:

```bash
# Install as package
pip install -e .
```

Then use command-line tools:
```bash
vp-train    # Run training pipeline
vp-test     # Run test suite
```

## 🎯 Expected Results

After running the pipeline, you'll get:

- **Trained Model**: `vp_prediction_outputs/training/best_vp_model.pth`
- **Performance Metrics**: R² scores, RMSE, MAE
- **Visualizations**: Training progress, validation results, prediction plots
- **Reports**: JSON files with detailed analysis

## 📈 Typical Performance

- **Training R²**: 0.75 - 0.90
- **Validation R²**: 0.70 - 0.85
- **Test R²**: 0.65 - 0.80
- **Training Time**: 10-30 minutes

## 🔧 Customization

### Individual Stages
```bash
# Train only
python train_vp_improved.py --stage training

# Validate only
python train_vp_improved.py --stage validation --model-path vp_prediction_outputs/training/best_vp_model.pth

# Predict only  
python train_vp_improved.py --stage prediction --model-path vp_prediction_outputs/training/best_vp_model.pth
```

### Custom Configuration
```bash
# Use custom settings
python train_vp_improved.py --config my_config.json --output-dir my_results
```

## 🆘 Need Help?

1. **Quick Start**: Read `VP_QUICK_START_GUIDE.md` for step-by-step instructions
2. **Test Setup**: Run `python test_vp_pipeline.py` to verify installation
3. **Detailed Docs**: Check `docs/` folder for comprehensive documentation
4. **Command Help**: Run `python train_vp_improved.py --help`

## 🏗️ Architecture

This pipeline implements a comprehensive three-stage VP prediction workflow:

- **Stage 1 - Training**: Model training with checkpointing and monitoring
- **Stage 2 - Validation**: Cross-validation and performance assessment  
- **Stage 3 - Prediction**: Model inference and result visualization

Built with professional software engineering practices:
- ✅ Modular, object-oriented design
- ✅ Comprehensive error handling and logging
- ✅ Configurable parameters and settings
- ✅ Organized output structure
- ✅ Extensive testing and validation

---

**🚀 Ready to start?** → Open **[VP_QUICK_START_GUIDE.md](VP_QUICK_START_GUIDE.md)** and follow the step-by-step instructions!
