﻿__init__.py
__pycache__
__pycache__\plot_vp_improved.cpython-310.pyc
__pycache__\train_vp_improved.cpython-310.pyc
1_List_of_cleaned.md
A1.hdf5
A1_converted.las
A2.hdf5
archives
archives\cleaned_files
archives\cleaned_files\20250906-1324_phase1
archives\cleaned_files\20250906-1324_phase2
archives\cleaned_files\20250906-1326_phase1
archives\cleaned_files\20250906-1326_phase1\cache_files
archives\cleaned_files\20250906-1326_phase1\test_outputs
archives\cleaned_files\20250906-1326_phase1\test_outputs\prediction
archives\cleaned_files\20250906-1326_phase1\test_outputs\prediction\detailed_predictions.json
archives\cleaned_files\20250906-1326_phase1\test_outputs\prediction\prediction_results.png
archives\cleaned_files\20250906-1326_phase1\test_outputs\training
archives\cleaned_files\20250906-1326_phase1\test_outputs\training\training_history.json
archives\cleaned_files\20250906-1326_phase1\test_outputs\training\training_progress.png
archives\cleaned_files\20250906-1326_phase1\test_outputs\validation
archives\cleaned_files\20250906-1326_phase1\test_outputs\validation\cv_results.json
archives\cleaned_files\20250906-1417_phase2
archives\cleaned_files\20250906-1417_phase2\dataset.py
archives\cleaned_files\20250906-1417_phase2\utils.py
archives\metadata
archives\metadata\pre_cleanup_inventory.csv
archives\metadata\protected_hdf5_files.csv
CLEANUP_COMPLETION_REPORT.md
Codebase.md
docs
docs\plan
docs\plan\1a_plan_refactoring_vp_ph1.md
plot_vp_improved.py
POST_CLEANUP_INVENTORY.txt
README.md
requirements.txt
setup.py
SETUP_CHECKLIST.md
test_plotting_module.py
test_vp_pipeline.py
test_well.las
train_vp_improved.py
visualize_vp_results.py
vp_prediction_outputs
vp_prediction_outputs\pipeline.log
vp_prediction_outputs\pipeline_config.json
vp_prediction_outputs\pipeline_summary.json
vp_prediction_outputs\prediction
vp_prediction_outputs\prediction\detailed_predictions.json
vp_prediction_outputs\prediction\prediction.log
vp_prediction_outputs\prediction\prediction_report.json
vp_prediction_outputs\prediction\prediction_results.png
vp_prediction_outputs\training
vp_prediction_outputs\training\best_vp_model.pth
vp_prediction_outputs\training\training.log
vp_prediction_outputs\training\training_history.json
vp_prediction_outputs\training\training_progress.png
vp_prediction_outputs\training\training_results.json
vp_prediction_outputs\validation
vp_prediction_outputs\validation\cv_results.json
vp_prediction_outputs\validation\cv_results.png
vp_prediction_outputs\validation\validation.log
vp_prediction_outputs\validation\validation_report.json
vp_predictor
vp_predictor\__init__.py
vp_predictor\__pycache__
vp_predictor\__pycache__\__init__.cpython-310.pyc
vp_predictor\__pycache__\las_processor.cpython-310.pyc
vp_predictor\__pycache__\model.cpython-310.pyc
vp_predictor\__pycache__\predictor.cpython-310.pyc
vp_predictor\__pycache__\utils.cpython-310.pyc
vp_predictor\__pycache__\vp_model_improved.cpython-310.pyc
vp_predictor\api
vp_predictor\api\__init__.py
vp_predictor\api\__pycache__
vp_predictor\api\__pycache__\__init__.cpython-310.pyc
vp_predictor\api\__pycache__\legacy.cpython-310.pyc
vp_predictor\api\__pycache__\predictor.cpython-310.pyc
vp_predictor\api\legacy.py
vp_predictor\api\predictor.py
vp_predictor\configs
vp_predictor\configs\__init__.py
vp_predictor\configs\__pycache__
vp_predictor\configs\__pycache__\__init__.cpython-310.pyc
vp_predictor\configs\__pycache__\curves.cpython-310.pyc
vp_predictor\configs\__pycache__\models.cpython-310.pyc
vp_predictor\configs\__pycache__\training.cpython-310.pyc
vp_predictor\configs\__pycache__\validation.cpython-310.pyc
vp_predictor\configs\curves.py
vp_predictor\configs\models.py
vp_predictor\configs\training.py
vp_predictor\configs\validation.py
vp_predictor\core
vp_predictor\core\__init__.py
vp_predictor\core\__pycache__
vp_predictor\core\__pycache__\__init__.cpython-310.pyc
vp_predictor\core\__pycache__\dataset.cpython-310.pyc
vp_predictor\core\__pycache__\decoder.cpython-310.pyc
vp_predictor\core\__pycache__\loss_functions.cpython-310.pyc
vp_predictor\core\__pycache__\normalizer.cpython-310.pyc
vp_predictor\core\__pycache__\training.cpython-310.pyc
vp_predictor\core\__pycache__\transformer.cpython-310.pyc
vp_predictor\core\dataset.py
vp_predictor\core\decoder.py
vp_predictor\core\loss_functions.py
vp_predictor\core\normalizer.py
vp_predictor\core\training.py
vp_predictor\core\transformer.py
vp_predictor\las_processor.py
vp_predictor\model.py
vp_predictor\predictor.py
vp_predictor\utils.py
vp_predictor\vp_model_improved.py
VP_QUICK_START_GUIDE.md
