"""
General Well Log Transformer - Main Architecture

This module implements the GeneralWellLogTransformer that extends VpTransformer
to support arbitrary input/output curve combinations while maintaining
backward compatibility.
"""

import torch
import torch.nn as nn
from typing import Dict, List, Optional, Union
from ..model import Input_Embedding, PositionalEncoding, TransformerBlock
from .decoder import GeneralDecoder, VpCompatibleDecoder
from ..configs import get_model_template, validate_full_config


class GeneralWellLogTransformer(nn.Module):
    """
    General transformer architecture for multi-curve well log prediction
    
    Extends VpTransformer to support:
    - Configurable input/output curve combinations
    - Multiple output curves with curve-specific processing
    - Flexible model sizing based on requirements
    - Backward compatibility with existing VpTransformer models
    """
    
    def __init__(self,
                 input_curves: List[str] = None,
                 output_curves: List[str] = None,
                 model_config: Dict = None,
                 template_name: str = None,
                 **kwargs):
        """
        Initialize GeneralWellLogTransformer
        
        Args:
            input_curves: List of input curve names (e.g., ['GR', 'CNL', 'DEN', 'RLLD'])
            output_curves: List of output curve names (e.g., ['VP', 'DEN'])
            model_config: Model architecture configuration
            template_name: Use predefined template (e.g., 'vp_prediction', 'multi_curve_basic')
            **kwargs: Additional model parameters (for backward compatibility)
        """
        super().__init__()
        
        # Handle template-based configuration
        if template_name:
            template = get_model_template(template_name)
            if input_curves is None:
                input_curves = template['input_curves']
            if output_curves is None:
                output_curves = template['output_curves']
            if model_config is None:
                model_config = template['model_config']
        
        # Default to backward compatible configuration
        if input_curves is None:
            input_curves = ['GR', 'CNL', 'DEN', 'RLLD']
        if output_curves is None:
            output_curves = ['VP']
        if model_config is None:
            model_config = {
                'res_blocks': 4,
                'encoder_layers': 4,
                'attention_heads': 4,
                'feature_dim': 64,
                'sequence_length': 640
            }
        
        # Store configuration
        self.input_curves = [curve.upper() for curve in input_curves]
        self.output_curves = [curve.upper() for curve in output_curves]
        self.model_config = model_config
        
        # Extract parameters with backward compatibility
        in_channels = len(self.input_curves)
        out_channels = len(self.output_curves)
        
        # Model parameters (with kwargs for backward compatibility)
        feature_num = kwargs.get('feature_num', model_config.get('feature_dim', 64))
        res_num = kwargs.get('res_num', model_config.get('res_blocks', 4))
        encoder_num = kwargs.get('encoder_num', model_config.get('encoder_layers', 4))
        num_heads = kwargs.get('num_heads', model_config.get('attention_heads', 4))
        seq_len = kwargs.get('seq_len', model_config.get('sequence_length', 640))
        
        # Other parameters with defaults
        use_pe = kwargs.get('use_pe', True)
        dim = kwargs.get('dim', feature_num)
        mlp_ratio = kwargs.get('mlp_ratio', 4.0)
        qkv_bias = kwargs.get('qkv_bias', False)
        drop = kwargs.get('drop', 0.0)
        attn_drop = kwargs.get('attn_drop', 0.0)
        position_drop = kwargs.get('position_drop', 0.0)
        act_layer = kwargs.get('act_layer', nn.GELU)
        norm_layer = kwargs.get('norm_layer', nn.LayerNorm)
        
        # Build transformer components (reuse existing architecture)
        self.feature_embedding = Input_Embedding(
            in_channels=in_channels,
            feature_num=feature_num,
            res_num=res_num
        )
        
        self.position_embedding = PositionalEncoding(
            d_model=dim,
            dropout=position_drop,
            seq_len=seq_len
        )
        self.use_pe = use_pe
        
        self.transformer_encoder = TransformerBlock(
            block_num=encoder_num,
            dim=dim,
            num_heads=num_heads,
            mlp_ratio=mlp_ratio,
            qkv_bias=qkv_bias,
            drop=drop,
            attn_drop=attn_drop,
            act_layer=act_layer,
            norm_layer=norm_layer
        )
        
        # Use GeneralDecoder for multi-curve support
        if len(self.output_curves) == 1 and self.output_curves[0] == 'VP':
            # Backward compatibility: use VpCompatibleDecoder for single Vp output
            self.decoder = VpCompatibleDecoder(
                res_num=res_num,
                out_channels=out_channels,
                feature_num=feature_num
            )
        else:
            # Multi-curve or non-Vp output: use GeneralDecoder
            self.decoder = GeneralDecoder(
                res_num=res_num,
                feature_num=feature_num,
                output_curves=self.output_curves
            )
    
    def forward(self, x: torch.Tensor) -> Union[torch.Tensor, Dict[str, torch.Tensor]]:
        """
        Forward pass through transformer
        
        Args:
            x: Input tensor [B, in_channels, L]
            
        Returns:
            For single output: torch.Tensor [B, 1, L]
            For multi-output: Dict[str, torch.Tensor] mapping curve names to outputs
        """
        # Feature embedding: [B, in_channels, L] → [B, feature_num, L]
        x = self.feature_embedding(x)
        
        # Prepare for transformer: [B, feature_num, L] → [B, L, feature_num]
        x = x.transpose(-2, -1)
        
        # Positional encoding
        if self.use_pe:
            x = self.position_embedding(x)
        
        # Transformer encoding: [B, L, feature_num] → [B, L, feature_num]
        x = self.transformer_encoder(x)
        
        # Prepare for decoder: [B, L, feature_num] → [B, feature_num, L]
        x = x.transpose(-2, -1)
        
        # Decode to output curves
        x = self.decoder(x)
        
        return x
    
    def get_model_info(self) -> Dict:
        """
        Get comprehensive model information
        
        Returns:
            dict: Model configuration and capabilities
        """
        return {
            'input_curves': self.input_curves,
            'output_curves': self.output_curves,
            'num_parameters': sum(p.numel() for p in self.parameters()),
            'model_config': self.model_config,
            'decoder_info': self.decoder.get_output_info() if hasattr(self.decoder, 'get_output_info') else {},
            'architecture': 'GeneralWellLogTransformer'
        }
    
    @classmethod
    def from_template(cls, template_name: str, **overrides):
        """
        Create model from predefined template
        
        Args:
            template_name: Template name (e.g., 'vp_prediction', 'multi_curve_basic')
            **overrides: Override template parameters
            
        Returns:
            GeneralWellLogTransformer: Configured model
        """
        template = get_model_template(template_name)
        
        # Apply overrides
        for key, value in overrides.items():
            if key in template:
                template[key] = value
            elif key in template['model_config']:
                template['model_config'][key] = value
        
        return cls(
            input_curves=template['input_curves'],
            output_curves=template['output_curves'],
            model_config=template['model_config']
        )
    
    @classmethod
    def from_config(cls, config: Dict):
        """
        Create model from complete configuration dictionary
        
        Args:
            config: Complete model configuration
            
        Returns:
            GeneralWellLogTransformer: Configured model
        """
        # Validate configuration
        is_valid, messages = validate_full_config(config)
        if not is_valid:
            raise ValueError(f"Invalid configuration: {messages}")
        
        return cls(
            input_curves=config['input_curves'],
            output_curves=config['output_curves'],
            model_config=config['model_config']
        )


# Predefined model configurations for common use cases
def GWLT_VpPrediction(**kwargs):
    """Vp prediction model (backward compatible)"""
    return GeneralWellLogTransformer.from_template('vp_prediction', **kwargs)

def GWLT_DensityPrediction(**kwargs):
    """Density prediction model"""
    return GeneralWellLogTransformer.from_template('density_prediction', **kwargs)

def GWLT_MultiCurve(**kwargs):
    """Multi-curve prediction model"""
    return GeneralWellLogTransformer.from_template('multi_curve_basic', **kwargs)

def GWLT_MissingSectionFill(**kwargs):
    """Missing section filling model"""
    return GeneralWellLogTransformer.from_template('missing_section_fill', **kwargs)

def GWLT_FastInference(**kwargs):
    """Fast inference model (optimized for speed)"""
    return GeneralWellLogTransformer.from_template('fast_inference', **kwargs)


# Model size variants for GeneralWellLogTransformer
def GWLT_Small(input_curves=None, output_curves=None, **kwargs):
    """Small general transformer model"""
    model_config = {
        'res_blocks': 2,
        'encoder_layers': 2,
        'attention_heads': 2,
        'feature_dim': 64,
        'sequence_length': 640
    }
    return GeneralWellLogTransformer(
        input_curves=input_curves,
        output_curves=output_curves,
        model_config=model_config,
        **kwargs
    )

def GWLT_Base(input_curves=None, output_curves=None, **kwargs):
    """Base general transformer model"""
    model_config = {
        'res_blocks': 4,
        'encoder_layers': 4,
        'attention_heads': 4,
        'feature_dim': 64,
        'sequence_length': 640
    }
    return GeneralWellLogTransformer(
        input_curves=input_curves,
        output_curves=output_curves,
        model_config=model_config,
        **kwargs
    )

def GWLT_Large(input_curves=None, output_curves=None, **kwargs):
    """Large general transformer model"""
    model_config = {
        'res_blocks': 6,
        'encoder_layers': 6,
        'attention_heads': 8,
        'feature_dim': 128,
        'sequence_length': 640
    }
    return GeneralWellLogTransformer(
        input_curves=input_curves,
        output_curves=output_curves,
        model_config=model_config,
        **kwargs
    )


# Backward compatibility aliases
def VpTransformer_Compatible(**kwargs):
    """Exact backward compatible replacement for VpTransformer"""
    return GeneralWellLogTransformer(
        input_curves=['GR', 'CNL', 'DEN', 'RLLD'],
        output_curves=['VP'],
        **kwargs
    )

# Size variants with backward compatibility
def GWLT_Vp_Small(**kwargs):
    """Small Vp transformer (backward compatible)"""
    return GWLT_Small(
        input_curves=['GR', 'CNL', 'DEN', 'RLLD'],
        output_curves=['VP'],
        **kwargs
    )

def GWLT_Vp_Base(**kwargs):
    """Base Vp transformer (backward compatible)"""
    return GWLT_Base(
        input_curves=['GR', 'CNL', 'DEN', 'RLLD'],
        output_curves=['VP'],
        **kwargs
    )

def GWLT_Vp_Large(**kwargs):
    """Large Vp transformer (backward compatible)"""
    return GWLT_Large(
        input_curves=['GR', 'CNL', 'DEN', 'RLLD'],
        output_curves=['VP'],
        **kwargs
    )