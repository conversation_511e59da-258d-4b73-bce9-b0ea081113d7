"""
Generalized data normalization for multi-curve well log prediction

This module extends the original VpDataNormalizer to support arbitrary
curve combinations with curve-specific normalization methods.
"""

import torch
import numpy as np
from typing import Dict, List, Optional, Union, Tuple
from ..configs import get_curve_config, CURVE_CONFIGURATIONS


class GeneralDataNormalizer:
    """
    Generalized data normalizer for multi-curve well log prediction
    
    Extends the original VpDataNormalizer to support:
    - Multiple input and output curves with curve-specific parameters
    - Different normalization methods (zscore, minmax, log_zscore, robust)
    - Flexible preprocessing (log transforms, etc.)
    - Automatic curve configuration loading
    
    This replaces hardcoded Vp-specific normalization parameters.
    """
    
    def __init__(self, 
                 input_curves: List[str] = None,
                 output_curves: List[str] = None,
                 curve_stats: Dict[str, Dict] = None,
                 custom_stats: Dict[str, Dict] = None):
        """
        Initialize GeneralDataNormalizer
        
        Args:
            input_curves: List of input curve names (e.g., ['GR', 'CNL', 'DEN', 'RLLD'])
            output_curves: List of output curve names (e.g., ['VP', 'DEN'])
            curve_stats: Override curve statistics (optional)
            custom_stats: Additional custom curve statistics (optional)
        """
        # Default to backward compatible configuration
        if input_curves is None:
            input_curves = ['GR', 'CNL', 'DEN', 'RLLD']
        if output_curves is None:
            output_curves = ['VP']
            
        self.input_curves = [curve.upper() for curve in input_curves]
        self.output_curves = [curve.upper() for curve in output_curves]
        self.all_curves = list(set(self.input_curves + self.output_curves))
        
        # Load curve configurations and build normalization parameters
        self.curve_configs = {}
        self.normalization_params = {}
        
        for curve in self.all_curves:
            try:
                config = get_curve_config(curve)
                self.curve_configs[curve] = config
                
                # Extract normalization parameters
                norm_config = config['normalization']
                self.normalization_params[curve] = {
                    'method': norm_config['method'],
                    'mean': norm_config.get('mean', 0.0),
                    'std': norm_config.get('std', 1.0),
                    'min': config['physics_range'][0],
                    'max': config['physics_range'][1],
                    'clip_range': norm_config.get('clip_range', (-3, 3)),
                    'log_base': norm_config.get('log_base', 10),
                    'preprocessing': config.get('preprocessing', None)
                }
            except ValueError:
                # Unknown curve, use default parameters
                self.curve_configs[curve] = {
                    'name': curve,
                    'unit': 'unknown',
                    'type': 'both',
                    'physics_range': (0, 1),
                    'normalization': {'method': 'zscore'},
                    'preprocessing': None
                }
                self.normalization_params[curve] = {
                    'method': 'zscore',
                    'mean': 0.0,
                    'std': 1.0,
                    'min': 0.0,
                    'max': 1.0,
                    'clip_range': (-3, 3),
                    'log_base': 10,
                    'preprocessing': None
                }
        
        # Override with custom statistics if provided
        if curve_stats:
            for curve, stats in curve_stats.items():
                if curve.upper() in self.normalization_params:
                    self.normalization_params[curve.upper()].update(stats)
        
        if custom_stats:
            for curve, stats in custom_stats.items():
                self.normalization_params[curve.upper()] = stats
    
    def normalize_inputs(self, curves_dict: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        """
        Normalize input curves according to their specific configurations
        
        Args:
            curves_dict: Dictionary mapping curve names to data tensors
            
        Returns:
            Dict[str, torch.Tensor]: Normalized curves
        """
        normalized = {}
        
        for curve_name, data in curves_dict.items():
            curve_name_upper = curve_name.upper()
            
            if curve_name_upper in self.normalization_params:
                normalized[curve_name] = self._normalize_curve(
                    data, curve_name_upper, is_target=False
                )
            else:
                # Unknown curve, return as-is with warning
                print(f"Warning: No normalization parameters for curve {curve_name}")
                normalized[curve_name] = data
        
        return normalized
    
    def normalize_targets(self, curves_dict: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        """
        Normalize target/output curves for training
        
        Args:
            curves_dict: Dictionary mapping curve names to target data
            
        Returns:
            Dict[str, torch.Tensor]: Normalized target curves
        """
        normalized = {}
        
        for curve_name, data in curves_dict.items():
            curve_name_upper = curve_name.upper()
            
            if curve_name_upper in self.normalization_params:
                normalized[curve_name] = self._normalize_curve(
                    data, curve_name_upper, is_target=True
                )
            else:
                print(f"Warning: No normalization parameters for target curve {curve_name}")
                normalized[curve_name] = data
        
        return normalized
    
    def denormalize_predictions(self, predictions: Union[torch.Tensor, Dict[str, torch.Tensor]], 
                               curve_names: List[str] = None) -> Union[torch.Tensor, Dict[str, torch.Tensor]]:
        """
        Denormalize predictions back to physical units
        
        Args:
            predictions: Normalized predictions (tensor or dict of tensors)
            curve_names: Names of curves (required if predictions is tensor)
            
        Returns:
            Denormalized predictions in physical units
        """
        if isinstance(predictions, dict):
            denormalized = {}
            for curve_name, pred_data in predictions.items():
                curve_name_upper = curve_name.upper()
                denormalized[curve_name] = self._denormalize_curve(
                    pred_data, curve_name_upper
                )
            return denormalized
        
        else:
            # Single tensor - need curve names
            if curve_names is None:
                curve_names = self.output_curves
            
            if len(curve_names) == 1:
                return self._denormalize_curve(predictions, curve_names[0])
            else:
                # Multi-channel tensor
                denormalized = {}
                for i, curve_name in enumerate(curve_names):
                    if predictions.dim() == 3:  # [B, C, L]
                        curve_pred = predictions[:, i:i+1, :]
                    else:  # [B, L]
                        curve_pred = predictions
                    denormalized[curve_name] = self._denormalize_curve(curve_pred, curve_name)
                return denormalized
    
    def _normalize_curve(self, data: torch.Tensor, curve_name: str, is_target: bool = False) -> torch.Tensor:
        """
        Normalize a single curve according to its configuration
        
        Args:
            data: Raw curve data
            curve_name: Name of the curve (uppercase)
            is_target: Whether this is target data (affects some normalizations)
            
        Returns:
            torch.Tensor: Normalized curve data
        """
        params = self.normalization_params[curve_name]
        
        # Apply preprocessing if specified
        if params['preprocessing'] == 'log10':
            # Clamp to avoid log(0) or log(negative)
            data = torch.log10(torch.clamp(data, min=0.01))
        elif params['preprocessing'] == 'log':
            data = torch.log(torch.clamp(data, min=0.01))
        elif params['preprocessing'] == 'sqrt':
            data = torch.sqrt(torch.clamp(data, min=0))
        elif params['preprocessing'] == 'square':
            data = data ** 2
        
        # Apply normalization method
        if params['method'] == 'zscore':
            normalized = (data - params['mean']) / params['std']
        elif params['method'] == 'log_zscore':
            # For curves like resistivity that need log transformation
            log_data = torch.log10(torch.clamp(data, min=0.1))
            normalized = (log_data - params['mean']) / params['std']
        elif params['method'] == 'minmax':
            # Min-max normalization to [0, 1] then shift to [-1, 1]
            normalized = 2 * (data - params['min']) / (params['max'] - params['min']) - 1
        elif params['method'] == 'robust':
            # Robust normalization using median and IQR (more resistant to outliers)
            median = torch.median(data)
            q75 = torch.quantile(data, 0.75)
            q25 = torch.quantile(data, 0.25)
            iqr = q75 - q25
            normalized = (data - median) / (iqr + 1e-8)  # Add small epsilon to avoid division by zero
        else:
            # Unknown method, use zscore as default
            normalized = (data - params['mean']) / params['std']
        
        # Apply clipping to prevent extreme values
        clip_min, clip_max = params['clip_range']
        normalized = torch.clamp(normalized, clip_min, clip_max)
        
        # Additional scaling to [-1, 1] range for zscore methods
        if params['method'] in ['zscore', 'log_zscore', 'robust']:
            normalized = normalized / max(abs(clip_min), abs(clip_max))
        
        return normalized
    
    def _denormalize_curve(self, normalized_data: torch.Tensor, curve_name: str) -> torch.Tensor:
        """
        Denormalize a single curve back to physical units
        
        Args:
            normalized_data: Normalized curve data
            curve_name: Name of the curve (uppercase)
            
        Returns:
            torch.Tensor: Denormalized curve data in physical units
        """
        params = self.normalization_params[curve_name]
        
        # Reverse the normalization scaling
        if params['method'] in ['zscore', 'log_zscore', 'robust']:
            clip_min, clip_max = params['clip_range']
            scaled_data = normalized_data * max(abs(clip_min), abs(clip_max))
        else:
            scaled_data = normalized_data
        
        # Reverse normalization method
        if params['method'] == 'zscore':
            data = scaled_data * params['std'] + params['mean']
        elif params['method'] == 'log_zscore':
            # Reverse log-zscore normalization
            log_data = scaled_data * params['std'] + params['mean']
            data = torch.pow(10, log_data)
        elif params['method'] == 'minmax':
            # Reverse min-max normalization from [-1, 1] to original range
            data = (scaled_data + 1) * (params['max'] - params['min']) / 2 + params['min']
        elif params['method'] == 'robust':
            # For robust normalization, we would need stored median and IQR
            # For now, use zscore parameters as approximation
            data = scaled_data * params['std'] + params['mean']
        else:
            # Unknown method, assume zscore
            data = scaled_data * params['std'] + params['mean']
        
        # Reverse preprocessing if it was applied
        # Note: Some preprocessing is not reversible (like log), so we skip it
        
        # Clamp to physics range to ensure realistic values
        data = torch.clamp(data, params['min'], params['max'])
        
        return data
    
    def get_curve_info(self, curve_name: str) -> Dict:
        """
        Get normalization information for a specific curve
        
        Args:
            curve_name: Name of the curve
            
        Returns:
            dict: Curve configuration and normalization parameters
        """
        curve_name_upper = curve_name.upper()
        if curve_name_upper not in self.normalization_params:
            raise ValueError(f"No information available for curve: {curve_name}")
        
        return {
            'config': self.curve_configs[curve_name_upper],
            'normalization': self.normalization_params[curve_name_upper]
        }
    
    def update_curve_stats(self, curve_name: str, stats_dict: Dict):
        """
        Update normalization statistics for a curve (useful for fine-tuning)
        
        Args:
            curve_name: Name of the curve
            stats_dict: Dictionary with updated statistics
        """
        curve_name_upper = curve_name.upper()
        if curve_name_upper in self.normalization_params:
            self.normalization_params[curve_name_upper].update(stats_dict)
        else:
            raise ValueError(f"Cannot update unknown curve: {curve_name}")
    
    def compute_data_statistics(self, curves_dict: Dict[str, torch.Tensor]) -> Dict[str, Dict]:
        """
        Compute statistics from actual data (for updating normalization parameters)
        
        Args:
            curves_dict: Dictionary mapping curve names to data tensors
            
        Returns:
            dict: Computed statistics for each curve
        """
        stats = {}
        
        for curve_name, data in curves_dict.items():
            curve_name_upper = curve_name.upper()
            
            # Compute basic statistics
            data_flat = data.flatten()
            stats[curve_name_upper] = {
                'mean': float(torch.mean(data_flat)),
                'std': float(torch.std(data_flat)),
                'min': float(torch.min(data_flat)),
                'max': float(torch.max(data_flat)),
                'median': float(torch.median(data_flat)),
                'q25': float(torch.quantile(data_flat, 0.25)),
                'q75': float(torch.quantile(data_flat, 0.75))
            }
        
        return stats
    
    def normalize_curve(self, data: Union[torch.Tensor, np.ndarray], curve_name: str) -> Union[torch.Tensor, np.ndarray]:
        """
        Normalize a single curve - compatibility method for dataset integration
        
        Args:
            data: Raw curve data
            curve_name: Name of the curve (uppercase)
            
        Returns:
            Normalized curve data (same type as input)
        """
        # Ensure we have a torch tensor for processing
        was_numpy = isinstance(data, np.ndarray)
        if was_numpy:
            data_tensor = torch.from_numpy(data).float()
        else:
            data_tensor = data.float()
        
        # Determine if this is input or target curve
        is_target = curve_name in self.output_curves
        
        # Use appropriate normalization method
        if is_target:
            # For single target curve, create dict and extract result
            target_dict = {curve_name: data_tensor}
            normalized_dict = self.normalize_targets(target_dict)
            result_tensor = normalized_dict[curve_name]
        else:
            # For input curve, create dict and extract result
            input_dict = {curve_name: data_tensor}
            normalized_dict = self.normalize_inputs(input_dict)
            result_tensor = normalized_dict[curve_name]
        
        # Return in original format
        if was_numpy:
            return result_tensor.numpy()
        else:
            return result_tensor


class VpCompatibleNormalizer(GeneralDataNormalizer):
    """
    Backward compatible normalizer that mimics VpDataNormalizer behavior
    
    This ensures existing VpTransformer code continues to work while
    using the new GeneralDataNormalizer infrastructure.
    """
    
    def __init__(self):
        """Initialize backward compatible VpDataNormalizer"""
        super().__init__(
            input_curves=['GR', 'CNL', 'DEN', 'RLLD'],
            output_curves=['VP']
        )
    
    def normalize_vp(self, vp_data: torch.Tensor) -> torch.Tensor:
        """
        Normalize Vp values for training (backward compatibility method)
        
        Args:
            vp_data: Raw Vp data
            
        Returns:
            torch.Tensor: Normalized Vp data
        """
        return self._normalize_curve(vp_data, 'VP', is_target=True)
    
    def denormalize_vp(self, normalized_vp: torch.Tensor) -> torch.Tensor:
        """
        Convert normalized Vp back to physical units (backward compatibility)
        
        Args:
            normalized_vp: Normalized Vp data
            
        Returns:
            torch.Tensor: Vp in physical units (μs/ft)
        """
        return self._denormalize_curve(normalized_vp, 'VP')


# Factory function for creating normalizers
def create_normalizer(normalizer_type: str = 'general', **kwargs) -> GeneralDataNormalizer:
    """
    Factory function for creating different types of normalizers
    
    Args:
        normalizer_type: Type of normalizer ('general', 'vp_compatible')
        **kwargs: Arguments passed to normalizer constructor
        
    Returns:
        GeneralDataNormalizer: Normalizer instance
    """
    if normalizer_type == 'general':
        return GeneralDataNormalizer(**kwargs)
    elif normalizer_type == 'vp_compatible':
        return VpCompatibleNormalizer()
    else:
        raise ValueError(f"Unknown normalizer type: {normalizer_type}")


# Backward compatibility aliases
VpDataNormalizer = VpCompatibleNormalizer  # Exact replacement for original