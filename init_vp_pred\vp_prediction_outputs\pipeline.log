2025-08-30 17:24:32,666 - VpPredictionPipeline - INFO - VP Prediction Pipeline initialized
2025-08-30 17:24:32,667 - VpPredictionPipeline - INFO - Output directory: D:\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\init_vp_pred\vp_prediction_outputs
2025-08-30 17:24:32,667 - VpPredictionPipeline - INFO - Using vp_predictor package: True
2025-08-30 17:24:32,668 - VpPredictionPipeline - INFO - VP Prediction Pipeline initialized
2025-08-30 17:24:32,669 - VpPredictionPipeline - INFO - Output directory: D:\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\init_vp_pred\test_vp_outputs
2025-08-30 17:24:32,669 - VpPredictionPipeline - INFO - Using vp_predictor package: True
2025-08-30 17:24:32,670 - VpPredictionPipeline - INFO - Created directory: test_vp_outputs\training
2025-08-30 17:24:32,671 - VpPredictionPipeline - INFO - Created directory: test_vp_outputs\validation
2025-08-30 17:24:32,672 - VpPredictionPipeline - INFO - Created directory: test_vp_outputs\prediction
2025-08-30 17:24:32,673 - VpPredictionPipeline - INFO - Configuration saved to: test_vp_outputs\pipeline_config.json
2025-08-30 17:25:56,593 - VpPredictionPipeline - INFO - VP Prediction Pipeline initialized
2025-08-30 17:25:56,593 - VpPredictionPipeline - INFO - Output directory: D:\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\init_vp_pred\vp_prediction_outputs
2025-08-30 17:25:56,593 - VpPredictionPipeline - INFO - Using vp_predictor package: True
2025-08-30 17:25:56,594 - VpPredictionPipeline - INFO - ================================================================================
2025-08-30 17:25:56,594 - VpPredictionPipeline - INFO - STARTING COMPREHENSIVE VP PREDICTION PIPELINE
2025-08-30 17:25:56,594 - VpPredictionPipeline - INFO - ================================================================================
2025-08-30 17:25:56,594 - VpPredictionPipeline - INFO - Created directory: vp_prediction_outputs\training
2025-08-30 17:25:56,595 - VpPredictionPipeline - INFO - Created directory: vp_prediction_outputs\validation
2025-08-30 17:25:56,596 - VpPredictionPipeline - INFO - Created directory: vp_prediction_outputs\prediction
2025-08-30 17:25:56,598 - VpPredictionPipeline - INFO - Configuration saved to: vp_prediction_outputs\pipeline_config.json
2025-08-30 17:25:56,598 - VpPredictionPipeline - INFO - 
============================================================
2025-08-30 17:25:56,599 - VpPredictionPipeline - INFO - STAGE 1: VP MODEL TRAINING
2025-08-30 17:25:56,599 - VpPredictionPipeline - INFO - ============================================================
2025-08-30 17:26:39,228 - VpPredictionPipeline - INFO - ✅ Training stage completed successfully
2025-08-30 17:26:39,228 - VpPredictionPipeline - INFO -    Best R²: 0.8642
2025-08-30 17:26:39,228 - VpPredictionPipeline - INFO -    Best RMSE: 0.16
2025-08-30 17:26:39,228 - VpPredictionPipeline - INFO - 
============================================================
2025-08-30 17:26:39,228 - VpPredictionPipeline - INFO - STAGE 2: VP MODEL VALIDATION
2025-08-30 17:26:39,229 - VpPredictionPipeline - INFO - ============================================================
2025-08-30 17:26:40,580 - VpPredictionPipeline - INFO - ✅ Validation stage completed successfully
2025-08-30 17:26:40,581 - VpPredictionPipeline - INFO -    Mean CV R²: 0.9330 ± 0.0378
2025-08-30 17:26:40,581 - VpPredictionPipeline - INFO -    Performance: Excellent
2025-08-30 17:26:40,581 - VpPredictionPipeline - INFO - 
============================================================
2025-08-30 17:26:40,581 - VpPredictionPipeline - INFO - STAGE 3: VP MODEL PREDICTION
2025-08-30 17:26:40,582 - VpPredictionPipeline - INFO - ============================================================
2025-08-30 17:26:42,831 - VpPredictionPipeline - INFO - ✅ Prediction stage completed successfully
2025-08-30 17:26:42,832 - VpPredictionPipeline - INFO -    Test R²: 0.9159
2025-08-30 17:26:42,832 - VpPredictionPipeline - INFO -    Test RMSE: 0.08
2025-08-30 17:26:42,833 - VpPredictionPipeline - INFO - 
================================================================================
2025-08-30 17:26:42,834 - VpPredictionPipeline - INFO - VP PREDICTION PIPELINE COMPLETED SUCCESSFULLY
2025-08-30 17:26:42,834 - VpPredictionPipeline - INFO - ================================================================================
2025-08-30 17:26:42,835 - VpPredictionPipeline - INFO - Total execution time: 46.24 seconds
2025-08-30 17:26:42,835 - VpPredictionPipeline - INFO - Results saved to: D:\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\init_vp_pred\vp_prediction_outputs
2025-08-30 20:11:04,346 - VpPredictionPipeline - INFO - VP Prediction Pipeline initialized
2025-08-30 20:11:04,347 - VpPredictionPipeline - INFO - Output directory: D:\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\init_vp_pred\vp_prediction_outputs
2025-08-30 20:11:04,347 - VpPredictionPipeline - INFO - Using vp_predictor package: True
2025-08-30 20:11:04,348 - VpPredictionPipeline - INFO - VP Prediction Pipeline initialized
2025-08-30 20:11:04,349 - VpPredictionPipeline - INFO - Output directory: D:\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\init_vp_pred\test_vp_outputs
2025-08-30 20:11:04,350 - VpPredictionPipeline - INFO - Using vp_predictor package: True
2025-08-30 20:11:04,351 - VpPredictionPipeline - INFO - Created directory: test_vp_outputs\training
2025-08-30 20:11:04,353 - VpPredictionPipeline - INFO - Created directory: test_vp_outputs\validation
2025-08-30 20:11:04,354 - VpPredictionPipeline - INFO - Created directory: test_vp_outputs\prediction
2025-08-30 20:11:04,356 - VpPredictionPipeline - INFO - Configuration saved to: test_vp_outputs\pipeline_config.json
2025-08-30 20:11:34,687 - VpPredictionPipeline - INFO - VP Prediction Pipeline initialized
2025-08-30 20:11:34,687 - VpPredictionPipeline - INFO - Output directory: D:\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\init_vp_pred\vp_prediction_outputs
2025-08-30 20:11:34,688 - VpPredictionPipeline - INFO - Using vp_predictor package: True
2025-08-30 20:11:34,689 - VpPredictionPipeline - INFO - ================================================================================
2025-08-30 20:11:34,689 - VpPredictionPipeline - INFO - STARTING COMPREHENSIVE VP PREDICTION PIPELINE
2025-08-30 20:11:34,690 - VpPredictionPipeline - INFO - ================================================================================
2025-08-30 20:11:34,691 - VpPredictionPipeline - INFO - Created directory: vp_prediction_outputs\training
2025-08-30 20:11:34,692 - VpPredictionPipeline - INFO - Created directory: vp_prediction_outputs\validation
2025-08-30 20:11:34,693 - VpPredictionPipeline - INFO - Created directory: vp_prediction_outputs\prediction
2025-08-30 20:11:34,694 - VpPredictionPipeline - INFO - Configuration saved to: vp_prediction_outputs\pipeline_config.json
2025-08-30 20:11:34,695 - VpPredictionPipeline - INFO - 
============================================================
2025-08-30 20:11:34,695 - VpPredictionPipeline - INFO - STAGE 1: VP MODEL TRAINING
2025-08-30 20:11:34,696 - VpPredictionPipeline - INFO - ============================================================
2025-08-30 20:12:19,186 - VpPredictionPipeline - INFO - ✅ Training stage completed successfully
2025-08-30 20:12:19,186 - VpPredictionPipeline - INFO -    Best R²: 0.8904
2025-08-30 20:12:19,187 - VpPredictionPipeline - INFO -    Best RMSE: 0.14
2025-08-30 20:12:19,187 - VpPredictionPipeline - INFO - 
============================================================
2025-08-30 20:12:19,188 - VpPredictionPipeline - INFO - STAGE 2: VP MODEL VALIDATION
2025-08-30 20:12:19,188 - VpPredictionPipeline - INFO - ============================================================
2025-08-30 20:12:20,691 - VpPredictionPipeline - INFO - ✅ Validation stage completed successfully
2025-08-30 20:12:20,692 - VpPredictionPipeline - INFO -    Mean CV R²: 0.9357 ± 0.0284
2025-08-30 20:12:20,693 - VpPredictionPipeline - INFO -    Performance: Excellent
2025-08-30 20:12:20,693 - VpPredictionPipeline - INFO - 
============================================================
2025-08-30 20:12:20,694 - VpPredictionPipeline - INFO - STAGE 3: VP MODEL PREDICTION
2025-08-30 20:12:20,694 - VpPredictionPipeline - INFO - ============================================================
2025-08-30 20:12:22,533 - VpPredictionPipeline - INFO - ✅ Prediction stage completed successfully
2025-08-30 20:12:22,534 - VpPredictionPipeline - INFO -    Test R²: 0.8793
2025-08-30 20:12:22,534 - VpPredictionPipeline - INFO -    Test RMSE: 0.10
2025-08-30 20:12:22,537 - VpPredictionPipeline - INFO - 
================================================================================
2025-08-30 20:12:22,537 - VpPredictionPipeline - INFO - VP PREDICTION PIPELINE COMPLETED SUCCESSFULLY
2025-08-30 20:12:22,538 - VpPredictionPipeline - INFO - ================================================================================
2025-08-30 20:12:22,538 - VpPredictionPipeline - INFO - Total execution time: 47.84 seconds
2025-08-30 20:12:22,539 - VpPredictionPipeline - INFO - Results saved to: D:\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\init_vp_pred\vp_prediction_outputs
2025-08-31 07:00:59,298 - VpPredictionPipeline - INFO - VP Prediction Pipeline initialized
2025-08-31 07:00:59,298 - VpPredictionPipeline - INFO - Output directory: C:\Users\<USER>\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\init_vp_pred\vp_prediction_outputs
2025-08-31 07:00:59,298 - VpPredictionPipeline - INFO - Using vp_predictor package: True
2025-08-31 07:00:59,298 - VpPredictionPipeline - INFO - VP Prediction Pipeline initialized
2025-08-31 07:00:59,305 - VpPredictionPipeline - INFO - Output directory: C:\Users\<USER>\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\init_vp_pred\test_vp_outputs
2025-08-31 07:00:59,305 - VpPredictionPipeline - INFO - Using vp_predictor package: True
2025-08-31 07:00:59,308 - VpPredictionPipeline - INFO - Created directory: test_vp_outputs\training
2025-08-31 07:00:59,308 - VpPredictionPipeline - INFO - Created directory: test_vp_outputs\validation
2025-08-31 07:00:59,314 - VpPredictionPipeline - INFO - Created directory: test_vp_outputs\prediction
2025-08-31 07:00:59,314 - VpPredictionPipeline - INFO - Configuration saved to: test_vp_outputs\pipeline_config.json
2025-08-31 07:02:43,962 - VpPredictionPipeline - INFO - VP Prediction Pipeline initialized
2025-08-31 07:02:43,962 - VpPredictionPipeline - INFO - Output directory: C:\Users\<USER>\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\init_vp_pred\vp_prediction_outputs
2025-08-31 07:02:43,970 - VpPredictionPipeline - INFO - Using vp_predictor package: True
2025-08-31 07:02:43,970 - VpPredictionPipeline - INFO - ================================================================================
2025-08-31 07:02:43,971 - VpPredictionPipeline - INFO - STARTING COMPREHENSIVE VP PREDICTION PIPELINE
2025-08-31 07:02:43,971 - VpPredictionPipeline - INFO - ================================================================================
2025-08-31 07:02:43,971 - VpPredictionPipeline - INFO - Created directory: vp_prediction_outputs\training
2025-08-31 07:02:43,971 - VpPredictionPipeline - INFO - Created directory: vp_prediction_outputs\validation
2025-08-31 07:02:43,971 - VpPredictionPipeline - INFO - Created directory: vp_prediction_outputs\prediction
2025-08-31 07:02:43,971 - VpPredictionPipeline - INFO - Configuration saved to: vp_prediction_outputs\pipeline_config.json
2025-08-31 07:02:43,971 - VpPredictionPipeline - INFO - 
============================================================
2025-08-31 07:02:43,971 - VpPredictionPipeline - INFO - STAGE 1: VP MODEL TRAINING
2025-08-31 07:02:43,971 - VpPredictionPipeline - INFO - ============================================================
2025-08-31 07:04:48,808 - VpPredictionPipeline - INFO - ✅ Training stage completed successfully
2025-08-31 07:04:48,808 - VpPredictionPipeline - INFO -    Best R²: 0.8665
2025-08-31 07:04:48,808 - VpPredictionPipeline - INFO -    Best RMSE: 0.16
2025-08-31 07:04:48,808 - VpPredictionPipeline - INFO - 
============================================================
2025-08-31 07:04:48,808 - VpPredictionPipeline - INFO - STAGE 2: VP MODEL VALIDATION
2025-08-31 07:04:48,808 - VpPredictionPipeline - INFO - ============================================================
2025-08-31 07:04:50,639 - VpPredictionPipeline - INFO - ✅ Validation stage completed successfully
2025-08-31 07:04:50,639 - VpPredictionPipeline - INFO -    Mean CV R²: 0.9414 ± 0.0389
2025-08-31 07:04:50,639 - VpPredictionPipeline - INFO -    Performance: Excellent
2025-08-31 07:04:50,639 - VpPredictionPipeline - INFO - 
============================================================
2025-08-31 07:04:50,639 - VpPredictionPipeline - INFO - STAGE 3: VP MODEL PREDICTION
2025-08-31 07:04:50,639 - VpPredictionPipeline - INFO - ============================================================
2025-08-31 07:04:52,789 - VpPredictionPipeline - INFO - ✅ Prediction stage completed successfully
2025-08-31 07:04:52,789 - VpPredictionPipeline - INFO -    Test R²: 0.8796
2025-08-31 07:04:52,789 - VpPredictionPipeline - INFO -    Test RMSE: 0.10
2025-08-31 07:04:52,792 - VpPredictionPipeline - INFO - 
================================================================================
2025-08-31 07:04:52,792 - VpPredictionPipeline - INFO - VP PREDICTION PIPELINE COMPLETED SUCCESSFULLY
2025-08-31 07:04:52,792 - VpPredictionPipeline - INFO - ================================================================================
2025-08-31 07:04:52,792 - VpPredictionPipeline - INFO - Total execution time: 128.82 seconds
2025-08-31 07:04:52,792 - VpPredictionPipeline - INFO - Results saved to: C:\Users\<USER>\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\init_vp_pred\vp_prediction_outputs
2025-09-04 21:01:19,330 - VpPredictionPipeline - INFO - VP Prediction Pipeline initialized
2025-09-04 21:01:19,376 - VpPredictionPipeline - INFO - Output directory: D:\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\init_vp_pred\vp_prediction_outputs
2025-09-04 21:01:19,377 - VpPredictionPipeline - INFO - Using vp_predictor package: True
2025-09-04 21:01:19,377 - VpPredictionPipeline - INFO - ================================================================================
2025-09-04 21:01:19,378 - VpPredictionPipeline - INFO - STARTING COMPREHENSIVE VP PREDICTION PIPELINE
2025-09-04 21:01:19,378 - VpPredictionPipeline - INFO - ================================================================================
2025-09-04 21:01:19,379 - VpPredictionPipeline - INFO - Created directory: vp_prediction_outputs\training
2025-09-04 21:01:19,380 - VpPredictionPipeline - INFO - Created directory: vp_prediction_outputs\validation
2025-09-04 21:01:19,380 - VpPredictionPipeline - INFO - Created directory: vp_prediction_outputs\prediction
2025-09-04 21:01:19,382 - VpPredictionPipeline - INFO - Configuration saved to: vp_prediction_outputs\pipeline_config.json
2025-09-04 21:01:19,383 - VpPredictionPipeline - INFO - 
============================================================
2025-09-04 21:01:19,384 - VpPredictionPipeline - INFO - STAGE 1: VP MODEL TRAINING
2025-09-04 21:01:19,385 - VpPredictionPipeline - INFO - ============================================================
2025-09-04 21:01:57,534 - VpPredictionPipeline - INFO - ✅ Training stage completed successfully
2025-09-04 21:01:57,535 - VpPredictionPipeline - INFO -    Best R²: 0.8725
2025-09-04 21:01:57,535 - VpPredictionPipeline - INFO -    Best RMSE: 0.15
2025-09-04 21:01:57,536 - VpPredictionPipeline - INFO - 
============================================================
2025-09-04 21:01:57,536 - VpPredictionPipeline - INFO - STAGE 2: VP MODEL VALIDATION
2025-09-04 21:01:57,537 - VpPredictionPipeline - INFO - ============================================================
2025-09-04 21:01:58,736 - VpPredictionPipeline - INFO - ✅ Validation stage completed successfully
2025-09-04 21:01:58,749 - VpPredictionPipeline - INFO -    Mean CV R²: 0.9339 ± 0.0336
2025-09-04 21:01:58,749 - VpPredictionPipeline - INFO -    Performance: Excellent
2025-09-04 21:01:58,749 - VpPredictionPipeline - INFO - 
============================================================
2025-09-04 21:01:58,750 - VpPredictionPipeline - INFO - STAGE 3: VP MODEL PREDICTION
2025-09-04 21:01:58,750 - VpPredictionPipeline - INFO - ============================================================
2025-09-04 21:02:00,860 - VpPredictionPipeline - INFO - ✅ Prediction stage completed successfully
2025-09-04 21:02:00,860 - VpPredictionPipeline - INFO -    Test R²: 0.8775
2025-09-04 21:02:00,860 - VpPredictionPipeline - INFO -    Test RMSE: 0.10
2025-09-04 21:02:00,863 - VpPredictionPipeline - INFO - 
================================================================================
2025-09-04 21:02:00,863 - VpPredictionPipeline - INFO - VP PREDICTION PIPELINE COMPLETED SUCCESSFULLY
2025-09-04 21:02:00,864 - VpPredictionPipeline - INFO - ================================================================================
2025-09-04 21:02:00,864 - VpPredictionPipeline - INFO - Total execution time: 41.48 seconds
2025-09-04 21:02:00,865 - VpPredictionPipeline - INFO - Results saved to: D:\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\init_vp_pred\vp_prediction_outputs
2025-09-04 21:02:40,438 - VpPredictionPipeline - INFO - VP Prediction Pipeline initialized
2025-09-04 21:02:40,438 - VpPredictionPipeline - INFO - Output directory: D:\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\init_vp_pred\vp_prediction_outputs
2025-09-04 21:02:40,439 - VpPredictionPipeline - INFO - Using vp_predictor package: True
2025-09-04 21:02:40,439 - VpPredictionPipeline - INFO - ================================================================================
2025-09-04 21:02:40,440 - VpPredictionPipeline - INFO - STARTING COMPREHENSIVE VP PREDICTION PIPELINE
2025-09-04 21:02:40,440 - VpPredictionPipeline - INFO - ================================================================================
2025-09-04 21:02:40,441 - VpPredictionPipeline - INFO - Created directory: vp_prediction_outputs\training
2025-09-04 21:02:40,442 - VpPredictionPipeline - INFO - Created directory: vp_prediction_outputs\validation
2025-09-04 21:02:40,443 - VpPredictionPipeline - INFO - Created directory: vp_prediction_outputs\prediction
2025-09-04 21:02:40,445 - VpPredictionPipeline - INFO - Configuration saved to: vp_prediction_outputs\pipeline_config.json
2025-09-04 21:02:40,446 - VpPredictionPipeline - INFO - 
============================================================
2025-09-04 21:02:40,447 - VpPredictionPipeline - INFO - STAGE 1: VP MODEL TRAINING
2025-09-04 21:02:40,448 - VpPredictionPipeline - INFO - ============================================================
2025-09-04 21:03:10,132 - VpPredictionPipeline - INFO - ✅ Training stage completed successfully
2025-09-04 21:03:10,133 - VpPredictionPipeline - INFO -    Best R²: 0.8805
2025-09-04 21:03:10,134 - VpPredictionPipeline - INFO -    Best RMSE: 0.15
2025-09-04 21:03:10,134 - VpPredictionPipeline - INFO - 
============================================================
2025-09-04 21:03:10,135 - VpPredictionPipeline - INFO - STAGE 2: VP MODEL VALIDATION
2025-09-04 21:03:10,135 - VpPredictionPipeline - INFO - ============================================================
2025-09-04 21:03:11,344 - VpPredictionPipeline - INFO - ✅ Validation stage completed successfully
2025-09-04 21:03:11,345 - VpPredictionPipeline - INFO -    Mean CV R²: 0.9192 ± 0.0238
2025-09-04 21:03:11,346 - VpPredictionPipeline - INFO -    Performance: Excellent
2025-09-04 21:03:11,347 - VpPredictionPipeline - INFO - 
============================================================
2025-09-04 21:03:11,348 - VpPredictionPipeline - INFO - STAGE 3: VP MODEL PREDICTION
2025-09-04 21:03:11,349 - VpPredictionPipeline - INFO - ============================================================
2025-09-04 21:03:13,333 - VpPredictionPipeline - INFO - ✅ Prediction stage completed successfully
2025-09-04 21:03:13,334 - VpPredictionPipeline - INFO -    Test R²: 0.8903
2025-09-04 21:03:13,335 - VpPredictionPipeline - INFO -    Test RMSE: 0.10
2025-09-04 21:03:13,338 - VpPredictionPipeline - INFO - 
================================================================================
2025-09-04 21:03:13,338 - VpPredictionPipeline - INFO - VP PREDICTION PIPELINE COMPLETED SUCCESSFULLY
2025-09-04 21:03:13,338 - VpPredictionPipeline - INFO - ================================================================================
2025-09-04 21:03:13,338 - VpPredictionPipeline - INFO - Total execution time: 32.89 seconds
2025-09-04 21:03:13,339 - VpPredictionPipeline - INFO - Results saved to: D:\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\init_vp_pred\vp_prediction_outputs
2025-09-04 21:04:42,889 - VpPredictionPipeline - INFO - VP Prediction Pipeline initialized
2025-09-04 21:04:42,891 - VpPredictionPipeline - INFO - Output directory: D:\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\init_vp_pred\vp_prediction_outputs
2025-09-04 21:04:42,891 - VpPredictionPipeline - INFO - Using vp_predictor package: True
2025-09-04 21:04:42,891 - VpPredictionPipeline - INFO - ================================================================================
2025-09-04 21:04:42,892 - VpPredictionPipeline - INFO - STARTING COMPREHENSIVE VP PREDICTION PIPELINE
2025-09-04 21:04:42,892 - VpPredictionPipeline - INFO - ================================================================================
2025-09-04 21:04:42,893 - VpPredictionPipeline - INFO - Created directory: vp_prediction_outputs\training
2025-09-04 21:04:42,894 - VpPredictionPipeline - INFO - Created directory: vp_prediction_outputs\validation
2025-09-04 21:04:42,894 - VpPredictionPipeline - INFO - Created directory: vp_prediction_outputs\prediction
2025-09-04 21:04:42,896 - VpPredictionPipeline - INFO - Configuration saved to: vp_prediction_outputs\pipeline_config.json
2025-09-04 21:04:42,898 - VpPredictionPipeline - INFO - 
============================================================
2025-09-04 21:04:42,898 - VpPredictionPipeline - INFO - STAGE 1: VP MODEL TRAINING
2025-09-04 21:04:42,899 - VpPredictionPipeline - INFO - ============================================================
2025-09-04 21:05:21,379 - VpPredictionPipeline - INFO - ✅ Training stage completed successfully
2025-09-04 21:05:21,380 - VpPredictionPipeline - INFO -    Best R²: 0.8846
2025-09-04 21:05:21,381 - VpPredictionPipeline - INFO -    Best RMSE: 0.15
2025-09-04 21:05:21,381 - VpPredictionPipeline - INFO - 
============================================================
2025-09-04 21:05:21,382 - VpPredictionPipeline - INFO - STAGE 2: VP MODEL VALIDATION
2025-09-04 21:05:21,382 - VpPredictionPipeline - INFO - ============================================================
2025-09-04 21:05:22,793 - VpPredictionPipeline - INFO - ✅ Validation stage completed successfully
2025-09-04 21:05:22,794 - VpPredictionPipeline - INFO -    Mean CV R²: 0.9259 ± 0.0275
2025-09-04 21:05:22,794 - VpPredictionPipeline - INFO -    Performance: Excellent
2025-09-04 21:05:22,795 - VpPredictionPipeline - INFO - 
============================================================
2025-09-04 21:05:22,796 - VpPredictionPipeline - INFO - STAGE 3: VP MODEL PREDICTION
2025-09-04 21:05:22,796 - VpPredictionPipeline - INFO - ============================================================
2025-09-04 21:05:25,029 - VpPredictionPipeline - INFO - ✅ Prediction stage completed successfully
2025-09-04 21:05:25,030 - VpPredictionPipeline - INFO -    Test R²: 0.9025
2025-09-04 21:05:25,030 - VpPredictionPipeline - INFO -    Test RMSE: 0.09
2025-09-04 21:05:25,032 - VpPredictionPipeline - INFO - 
================================================================================
2025-09-04 21:05:25,033 - VpPredictionPipeline - INFO - VP PREDICTION PIPELINE COMPLETED SUCCESSFULLY
2025-09-04 21:05:25,034 - VpPredictionPipeline - INFO - ================================================================================
2025-09-04 21:05:25,035 - VpPredictionPipeline - INFO - Total execution time: 42.14 seconds
2025-09-04 21:05:25,036 - VpPredictionPipeline - INFO - Results saved to: D:\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\init_vp_pred\vp_prediction_outputs
2025-09-04 21:10:59,285 - VpPredictionPipeline - INFO - VP Prediction Pipeline initialized
2025-09-04 21:10:59,286 - VpPredictionPipeline - INFO - Output directory: D:\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\init_vp_pred\vp_prediction_outputs
2025-09-04 21:10:59,287 - VpPredictionPipeline - INFO - Using vp_predictor package: True
2025-09-04 21:10:59,288 - VpPredictionPipeline - INFO - ================================================================================
2025-09-04 21:10:59,288 - VpPredictionPipeline - INFO - STARTING COMPREHENSIVE VP PREDICTION PIPELINE
2025-09-04 21:10:59,289 - VpPredictionPipeline - INFO - ================================================================================
2025-09-04 21:10:59,290 - VpPredictionPipeline - INFO - Created directory: vp_prediction_outputs\training
2025-09-04 21:10:59,291 - VpPredictionPipeline - INFO - Created directory: vp_prediction_outputs\validation
2025-09-04 21:10:59,291 - VpPredictionPipeline - INFO - Created directory: vp_prediction_outputs\prediction
2025-09-04 21:10:59,293 - VpPredictionPipeline - INFO - Configuration saved to: vp_prediction_outputs\pipeline_config.json
2025-09-04 21:10:59,294 - VpPredictionPipeline - INFO - 
============================================================
2025-09-04 21:10:59,295 - VpPredictionPipeline - INFO - STAGE 1: VP MODEL TRAINING
2025-09-04 21:10:59,296 - VpPredictionPipeline - INFO - ============================================================
2025-09-04 21:12:32,478 - VpPredictionPipeline - INFO - ✅ Training stage completed successfully
2025-09-04 21:12:32,480 - VpPredictionPipeline - INFO -    Best R²: 0.8897
2025-09-04 21:12:32,481 - VpPredictionPipeline - INFO -    Best RMSE: 0.14
2025-09-04 21:12:32,483 - VpPredictionPipeline - INFO - 
============================================================
2025-09-04 21:12:32,484 - VpPredictionPipeline - INFO - STAGE 2: VP MODEL VALIDATION
2025-09-04 21:12:32,485 - VpPredictionPipeline - INFO - ============================================================
2025-09-04 21:12:33,987 - VpPredictionPipeline - INFO - ✅ Validation stage completed successfully
2025-09-04 21:12:33,988 - VpPredictionPipeline - INFO -    Mean CV R²: 0.9374 ± 0.0286
2025-09-04 21:12:33,989 - VpPredictionPipeline - INFO -    Performance: Excellent
2025-09-04 21:12:33,990 - VpPredictionPipeline - INFO - 
============================================================
2025-09-04 21:12:33,992 - VpPredictionPipeline - INFO - STAGE 3: VP MODEL PREDICTION
2025-09-04 21:12:33,992 - VpPredictionPipeline - INFO - ============================================================
2025-09-04 21:12:36,818 - VpPredictionPipeline - INFO - ✅ Prediction stage completed successfully
2025-09-04 21:12:36,819 - VpPredictionPipeline - INFO -    Test R²: 0.9169
2025-09-04 21:12:36,820 - VpPredictionPipeline - INFO -    Test RMSE: 0.08
2025-09-04 21:12:36,823 - VpPredictionPipeline - INFO - 
================================================================================
2025-09-04 21:12:36,825 - VpPredictionPipeline - INFO - VP PREDICTION PIPELINE COMPLETED SUCCESSFULLY
2025-09-04 21:12:36,826 - VpPredictionPipeline - INFO - ================================================================================
2025-09-04 21:12:36,827 - VpPredictionPipeline - INFO - Total execution time: 97.53 seconds
2025-09-04 21:12:36,828 - VpPredictionPipeline - INFO - Results saved to: D:\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\init_vp_pred\vp_prediction_outputs
2025-09-04 21:34:39,082 - VpPredictionPipeline - INFO - VP Prediction Pipeline initialized
2025-09-04 21:34:39,083 - VpPredictionPipeline - INFO - Output directory: D:\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\init_vp_pred\vp_prediction_outputs
2025-09-04 21:34:39,084 - VpPredictionPipeline - INFO - Using vp_predictor package: True
2025-09-04 21:34:39,084 - VpPredictionPipeline - INFO - ================================================================================
2025-09-04 21:34:39,084 - VpPredictionPipeline - INFO - STARTING COMPREHENSIVE VP PREDICTION PIPELINE
2025-09-04 21:34:39,084 - VpPredictionPipeline - INFO - ================================================================================
2025-09-04 21:34:39,085 - VpPredictionPipeline - INFO - Created directory: vp_prediction_outputs\training
2025-09-04 21:34:39,086 - VpPredictionPipeline - INFO - Created directory: vp_prediction_outputs\validation
2025-09-04 21:34:39,086 - VpPredictionPipeline - INFO - Created directory: vp_prediction_outputs\prediction
2025-09-04 21:34:39,087 - VpPredictionPipeline - INFO - Configuration saved to: vp_prediction_outputs\pipeline_config.json
2025-09-04 21:34:39,087 - VpPredictionPipeline - INFO - 
============================================================
2025-09-04 21:34:39,088 - VpPredictionPipeline - INFO - STAGE 1: VP MODEL TRAINING
2025-09-04 21:34:39,088 - VpPredictionPipeline - INFO - ============================================================
2025-09-04 21:35:24,938 - VpPredictionPipeline - INFO - ✅ Training stage completed successfully
2025-09-04 21:35:24,938 - VpPredictionPipeline - INFO -    Best R²: 0.8617
2025-09-04 21:35:24,939 - VpPredictionPipeline - INFO -    Best RMSE: 0.16
2025-09-04 21:35:24,939 - VpPredictionPipeline - INFO - 
============================================================
2025-09-04 21:35:24,940 - VpPredictionPipeline - INFO - STAGE 2: VP MODEL VALIDATION
2025-09-04 21:35:24,940 - VpPredictionPipeline - INFO - ============================================================
2025-09-04 21:35:26,859 - VpPredictionPipeline - INFO - ✅ Validation stage completed successfully
2025-09-04 21:35:26,860 - VpPredictionPipeline - INFO -    Mean CV R²: 0.9093 ± 0.0358
2025-09-04 21:35:26,860 - VpPredictionPipeline - INFO -    Performance: Excellent
2025-09-04 21:35:26,861 - VpPredictionPipeline - INFO - 
============================================================
2025-09-04 21:35:26,862 - VpPredictionPipeline - INFO - STAGE 3: VP MODEL PREDICTION
2025-09-04 21:35:26,862 - VpPredictionPipeline - INFO - ============================================================
2025-09-04 21:35:29,776 - VpPredictionPipeline - INFO - ✅ Prediction stage completed successfully
2025-09-04 21:35:29,778 - VpPredictionPipeline - INFO -    Test R²: 0.8885
2025-09-04 21:35:29,779 - VpPredictionPipeline - INFO -    Test RMSE: 0.10
2025-09-04 21:35:29,782 - VpPredictionPipeline - INFO - 
================================================================================
2025-09-04 21:35:29,783 - VpPredictionPipeline - INFO - VP PREDICTION PIPELINE COMPLETED SUCCESSFULLY
2025-09-04 21:35:29,784 - VpPredictionPipeline - INFO - ================================================================================
2025-09-04 21:35:29,786 - VpPredictionPipeline - INFO - Total execution time: 50.70 seconds
2025-09-04 21:35:29,787 - VpPredictionPipeline - INFO - Results saved to: D:\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\init_vp_pred\vp_prediction_outputs
2025-09-04 21:37:20,652 - VpPredictionPipeline - INFO - VP Prediction Pipeline initialized
2025-09-04 21:37:20,653 - VpPredictionPipeline - INFO - Output directory: D:\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\init_vp_pred\vp_prediction_outputs
2025-09-04 21:37:20,654 - VpPredictionPipeline - INFO - Using vp_predictor package: True
2025-09-04 21:37:21,112 - VpPredictionPipeline - INFO - ================================================================================
2025-09-04 21:37:21,113 - VpPredictionPipeline - INFO - STARTING COMPREHENSIVE VP PREDICTION PIPELINE
2025-09-04 21:37:21,114 - VpPredictionPipeline - INFO - ================================================================================
2025-09-04 21:37:21,116 - VpPredictionPipeline - INFO - Created directory: vp_prediction_outputs\training
2025-09-04 21:37:21,118 - VpPredictionPipeline - INFO - Created directory: vp_prediction_outputs\validation
2025-09-04 21:37:21,119 - VpPredictionPipeline - INFO - Created directory: vp_prediction_outputs\prediction
2025-09-04 21:37:21,120 - VpPredictionPipeline - INFO - Configuration saved to: vp_prediction_outputs\pipeline_config.json
2025-09-04 21:37:21,121 - VpPredictionPipeline - INFO - 
============================================================
2025-09-04 21:37:21,121 - VpPredictionPipeline - INFO - STAGE 1: VP MODEL TRAINING
2025-09-04 21:37:21,122 - VpPredictionPipeline - INFO - ============================================================
2025-09-04 21:38:03,079 - VpPredictionPipeline - INFO - ✅ Training stage completed successfully
2025-09-04 21:38:03,080 - VpPredictionPipeline - INFO -    Best R²: 0.8796
2025-09-04 21:38:03,080 - VpPredictionPipeline - INFO -    Best RMSE: 0.15
2025-09-04 21:38:03,080 - VpPredictionPipeline - INFO - 
============================================================
2025-09-04 21:38:03,081 - VpPredictionPipeline - INFO - STAGE 2: VP MODEL VALIDATION
2025-09-04 21:38:03,081 - VpPredictionPipeline - INFO - ============================================================
2025-09-04 21:38:05,138 - VpPredictionPipeline - INFO - ✅ Validation stage completed successfully
2025-09-04 21:38:05,142 - VpPredictionPipeline - INFO -    Mean CV R²: 0.9375 ± 0.0298
2025-09-04 21:38:05,143 - VpPredictionPipeline - INFO -    Performance: Excellent
2025-09-04 21:38:05,144 - VpPredictionPipeline - INFO - 
============================================================
2025-09-04 21:38:05,146 - VpPredictionPipeline - INFO - STAGE 3: VP MODEL PREDICTION
2025-09-04 21:38:05,148 - VpPredictionPipeline - INFO - ============================================================
2025-09-04 21:38:08,697 - VpPredictionPipeline - INFO - ✅ Prediction stage completed successfully
2025-09-04 21:38:08,698 - VpPredictionPipeline - INFO -    Test R²: 0.9065
2025-09-04 21:38:08,699 - VpPredictionPipeline - INFO -    Test RMSE: 0.09
2025-09-04 21:38:08,707 - VpPredictionPipeline - INFO - 
================================================================================
2025-09-04 21:38:08,710 - VpPredictionPipeline - INFO - VP PREDICTION PIPELINE COMPLETED SUCCESSFULLY
2025-09-04 21:38:08,712 - VpPredictionPipeline - INFO - ================================================================================
2025-09-04 21:38:08,713 - VpPredictionPipeline - INFO - Total execution time: 47.58 seconds
2025-09-04 21:38:08,715 - VpPredictionPipeline - INFO - Results saved to: D:\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\init_vp_pred\vp_prediction_outputs
2025-09-06 10:36:00,024 - VpPredictionPipeline - INFO - VP Prediction Pipeline initialized
2025-09-06 10:36:00,028 - VpPredictionPipeline - INFO - Output directory: C:\Users\<USER>\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\init_vp_pred\vp_prediction_outputs
2025-09-06 10:36:00,028 - VpPredictionPipeline - INFO - Using vp_predictor package: True
2025-09-06 10:36:00,029 - VpPredictionPipeline - INFO - ================================================================================
2025-09-06 10:36:00,029 - VpPredictionPipeline - INFO - STARTING COMPREHENSIVE VP PREDICTION PIPELINE
2025-09-06 10:36:00,029 - VpPredictionPipeline - INFO - ================================================================================
2025-09-06 10:36:00,029 - VpPredictionPipeline - INFO - Created directory: vp_prediction_outputs\training
2025-09-06 10:36:00,029 - VpPredictionPipeline - INFO - Created directory: vp_prediction_outputs\validation
2025-09-06 10:36:00,029 - VpPredictionPipeline - INFO - Created directory: vp_prediction_outputs\prediction
2025-09-06 10:36:00,035 - VpPredictionPipeline - INFO - Configuration saved to: vp_prediction_outputs\pipeline_config.json
2025-09-06 10:36:00,036 - VpPredictionPipeline - INFO - 
============================================================
2025-09-06 10:36:00,037 - VpPredictionPipeline - INFO - STAGE 1: VP MODEL TRAINING
2025-09-06 10:36:00,037 - VpPredictionPipeline - INFO - ============================================================
2025-09-06 10:43:29,711 - VpPredictionPipeline - INFO - ✅ Training stage completed successfully
2025-09-06 10:43:29,712 - VpPredictionPipeline - INFO -    Best R²: 0.8918
2025-09-06 10:43:29,712 - VpPredictionPipeline - INFO -    Best RMSE: 0.14
2025-09-06 10:43:29,712 - VpPredictionPipeline - INFO - 
============================================================
2025-09-06 10:43:29,712 - VpPredictionPipeline - INFO - STAGE 2: VP MODEL VALIDATION
2025-09-06 10:43:29,712 - VpPredictionPipeline - INFO - ============================================================
2025-09-06 10:43:31,297 - VpPredictionPipeline - INFO - ✅ Validation stage completed successfully
2025-09-06 10:43:31,299 - VpPredictionPipeline - INFO -    Mean CV R²: 0.9390 ± 0.0247
2025-09-06 10:43:31,299 - VpPredictionPipeline - INFO -    Performance: Excellent
2025-09-06 10:43:31,300 - VpPredictionPipeline - INFO - 
============================================================
2025-09-06 10:43:31,301 - VpPredictionPipeline - INFO - STAGE 3: VP MODEL PREDICTION
2025-09-06 10:43:31,301 - VpPredictionPipeline - INFO - ============================================================
2025-09-06 10:43:32,928 - VpPredictionPipeline - INFO - ✅ Prediction stage completed successfully
2025-09-06 10:43:32,928 - VpPredictionPipeline - INFO -    Test R²: 0.9025
2025-09-06 10:43:32,928 - VpPredictionPipeline - INFO -    Test RMSE: 0.09
2025-09-06 10:43:32,937 - VpPredictionPipeline - INFO - 
================================================================================
2025-09-06 10:43:32,937 - VpPredictionPipeline - INFO - VP PREDICTION PIPELINE COMPLETED SUCCESSFULLY
2025-09-06 10:43:32,939 - VpPredictionPipeline - INFO - ================================================================================
2025-09-06 10:43:32,939 - VpPredictionPipeline - INFO - Total execution time: 452.91 seconds
2025-09-06 10:43:32,940 - VpPredictionPipeline - INFO - Results saved to: C:\Users\<USER>\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\init_vp_pred\vp_prediction_outputs
2025-09-06 13:25:22,478 - VpPredictionPipeline - INFO - VP Prediction Pipeline initialized
2025-09-06 13:25:22,479 - VpPredictionPipeline - INFO - Output directory: C:\Users\<USER>\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\init_vp_pred\vp_prediction_outputs
2025-09-06 13:25:22,479 - VpPredictionPipeline - INFO - Using vp_predictor package: True
2025-09-06 13:25:22,482 - VpPredictionPipeline - INFO - VP Prediction Pipeline initialized
2025-09-06 13:25:22,483 - VpPredictionPipeline - INFO - Output directory: C:\Users\<USER>\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\init_vp_pred\test_vp_outputs
2025-09-06 13:25:22,484 - VpPredictionPipeline - INFO - Using vp_predictor package: True
2025-09-06 13:25:22,485 - VpPredictionPipeline - INFO - Created directory: test_vp_outputs\training
2025-09-06 13:25:22,487 - VpPredictionPipeline - INFO - Created directory: test_vp_outputs\validation
2025-09-06 13:25:22,489 - VpPredictionPipeline - INFO - Created directory: test_vp_outputs\prediction
2025-09-06 13:25:22,492 - VpPredictionPipeline - INFO - Configuration saved to: test_vp_outputs\pipeline_config.json
2025-09-06 13:26:58,737 - VpPredictionPipeline - INFO - VP Prediction Pipeline initialized
2025-09-06 13:26:58,738 - VpPredictionPipeline - INFO - Output directory: C:\Users\<USER>\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\init_vp_pred\vp_prediction_outputs
2025-09-06 13:26:58,738 - VpPredictionPipeline - INFO - Using vp_predictor package: True
2025-09-06 13:26:58,739 - VpPredictionPipeline - INFO - VP Prediction Pipeline initialized
2025-09-06 13:26:58,740 - VpPredictionPipeline - INFO - Output directory: C:\Users\<USER>\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\init_vp_pred\test_vp_outputs
2025-09-06 13:26:58,741 - VpPredictionPipeline - INFO - Using vp_predictor package: True
2025-09-06 13:26:58,742 - VpPredictionPipeline - INFO - Created directory: test_vp_outputs\training
2025-09-06 13:26:58,743 - VpPredictionPipeline - INFO - Created directory: test_vp_outputs\validation
2025-09-06 13:26:58,744 - VpPredictionPipeline - INFO - Created directory: test_vp_outputs\prediction
2025-09-06 13:26:58,745 - VpPredictionPipeline - INFO - Configuration saved to: test_vp_outputs\pipeline_config.json
2025-09-06 14:17:42,859 - VpPredictionPipeline - INFO - VP Prediction Pipeline initialized
2025-09-06 14:17:42,860 - VpPredictionPipeline - INFO - Output directory: C:\Users\<USER>\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\init_vp_pred\vp_prediction_outputs
2025-09-06 14:17:42,861 - VpPredictionPipeline - INFO - Using vp_predictor package: True
2025-09-06 14:17:42,864 - VpPredictionPipeline - INFO - VP Prediction Pipeline initialized
2025-09-06 14:17:42,865 - VpPredictionPipeline - INFO - Output directory: C:\Users\<USER>\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\init_vp_pred\test_vp_outputs
2025-09-06 14:17:42,865 - VpPredictionPipeline - INFO - Using vp_predictor package: True
2025-09-06 14:17:42,867 - VpPredictionPipeline - INFO - Created directory: test_vp_outputs\training
2025-09-06 14:17:42,868 - VpPredictionPipeline - INFO - Created directory: test_vp_outputs\validation
2025-09-06 14:17:42,870 - VpPredictionPipeline - INFO - Created directory: test_vp_outputs\prediction
2025-09-06 14:17:42,873 - VpPredictionPipeline - INFO - Configuration saved to: test_vp_outputs\pipeline_config.json
2025-09-06 14:21:05,681 - VpPredictionPipeline - INFO - VP Prediction Pipeline initialized
2025-09-06 14:21:05,682 - VpPredictionPipeline - INFO - Output directory: C:\Users\<USER>\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\init_vp_pred\vp_prediction_outputs
2025-09-06 14:21:05,683 - VpPredictionPipeline - INFO - Using vp_predictor package: True
2025-09-06 14:21:05,684 - VpPredictionPipeline - INFO - ================================================================================
2025-09-06 14:21:05,685 - VpPredictionPipeline - INFO - STARTING COMPREHENSIVE VP PREDICTION PIPELINE
2025-09-06 14:21:05,685 - VpPredictionPipeline - INFO - ================================================================================
2025-09-06 14:21:05,686 - VpPredictionPipeline - INFO - Created directory: vp_prediction_outputs\training
2025-09-06 14:21:05,687 - VpPredictionPipeline - INFO - Created directory: vp_prediction_outputs\validation
2025-09-06 14:21:05,688 - VpPredictionPipeline - INFO - Created directory: vp_prediction_outputs\prediction
2025-09-06 14:21:05,689 - VpPredictionPipeline - INFO - Configuration saved to: vp_prediction_outputs\pipeline_config.json
2025-09-06 14:21:05,690 - VpPredictionPipeline - INFO - 
============================================================
2025-09-06 14:21:05,690 - VpPredictionPipeline - INFO - STAGE 1: VP MODEL TRAINING
2025-09-06 14:21:05,691 - VpPredictionPipeline - INFO - ============================================================
2025-09-06 14:22:28,441 - VpPredictionPipeline - INFO - ✅ Training stage completed successfully
2025-09-06 14:22:28,441 - VpPredictionPipeline - INFO -    Best R²: 0.8562
2025-09-06 14:22:28,441 - VpPredictionPipeline - INFO -    Best RMSE: 0.16
2025-09-06 14:22:28,443 - VpPredictionPipeline - INFO - 
============================================================
2025-09-06 14:22:28,443 - VpPredictionPipeline - INFO - STAGE 2: VP MODEL VALIDATION
2025-09-06 14:22:28,443 - VpPredictionPipeline - INFO - ============================================================
2025-09-06 14:22:29,617 - VpPredictionPipeline - INFO - ✅ Validation stage completed successfully
2025-09-06 14:22:29,618 - VpPredictionPipeline - INFO -    Mean CV R²: 0.9328 ± 0.0419
2025-09-06 14:22:29,619 - VpPredictionPipeline - INFO -    Performance: Excellent
2025-09-06 14:22:29,619 - VpPredictionPipeline - INFO - 
============================================================
2025-09-06 14:22:29,619 - VpPredictionPipeline - INFO - STAGE 3: VP MODEL PREDICTION
2025-09-06 14:22:29,620 - VpPredictionPipeline - INFO - ============================================================
2025-09-06 14:22:30,908 - VpPredictionPipeline - INFO - ✅ Prediction stage completed successfully
2025-09-06 14:22:30,908 - VpPredictionPipeline - INFO -    Test R²: 0.8660
2025-09-06 14:22:30,909 - VpPredictionPipeline - INFO -    Test RMSE: 0.11
2025-09-06 14:22:30,910 - VpPredictionPipeline - INFO - 
================================================================================
2025-09-06 14:22:30,910 - VpPredictionPipeline - INFO - VP PREDICTION PIPELINE COMPLETED SUCCESSFULLY
2025-09-06 14:22:30,910 - VpPredictionPipeline - INFO - ================================================================================
2025-09-06 14:22:30,911 - VpPredictionPipeline - INFO - Total execution time: 85.22 seconds
2025-09-06 14:22:30,911 - VpPredictionPipeline - INFO - Results saved to: C:\Users\<USER>\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\init_vp_pred\vp_prediction_outputs
