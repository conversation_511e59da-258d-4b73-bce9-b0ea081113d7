# Codebase Index (Warp Quick Map)

This document helps you quickly locate the major components in this repository and understand how they relate. It’s optimized for terminal navigation and code search.

TL;DR – Where things are
- Model backbone (transformer): init_vp_pred/vp_predictor/core/transformer.py
- Decoders (single & multi-curve): init_vp_pred/vp_predictor/core/decoder.py
- Dataset/windowing: init_vp_pred/vp_predictor/core/dataset.py
- Normalization (per-curve, physics-aware): init_vp_pred/vp_predictor/core/normalizer.py
- Losses (curve-specific + physics constraints): init_vp_pred/vp_predictor/core/loss_functions.py
- Trainer (loops, schedulers, early stopping, AMP): init_vp_pred/vp_predictor/core/training.py
- Configs (curves/models/training/validation): init_vp_pred/vp_predictor/configs/
- Prediction API (general + legacy): init_vp_pred/vp_predictor/api/
- LAS/HDF5 processing: init_vp_pred/vp_predictor/las_processor.py
- Original MWLT models: init_vp_pred/vp_predictor/model.py and init_density_base/core/model.py
- Robust checkpoint I/O: init_density_base/core/utils.py (to be unified)
- Density improved pipeline: init_density_base/density_prediction_improved.py
- Generalization plan: 1_Generalization_Architecture.md

Directory-by-directory

init_vp_pred/
- vp_predictor/
  - core/
    - transformer.py
      - GeneralWellLogTransformer (configurable backbone)
      - GWLT_* factories (Small/Base/Large)
    - decoder.py
      - GeneralDecoder (per-curve activations)
      - VpCompatibleDecoder (back-compat)
      - MultiCurveDecoder (multi-output ready)
    - dataset.py
      - GeneralWellLogDataset (windowing 720→640, overlap via step_size)
    - normalizer.py
      - GeneralDataNormalizer (zscore/log/minmax/robust; physics ranges)
      - VpCompatibleNormalizer (alias)
    - loss_functions.py
      - GeneralWellLogLoss (MSE/MAE/Huber with constraints)
      - CurveSpecificLossFactory (create_vp_loss, create_density_loss, etc.)
    - training.py
      - GeneralTrainingManager (optimizer/scheduler/early stopping/AMP)
      - Helpers: create_vp_trainer, create_density_trainer
  - configs/
    - curves.py (CURVE_CONFIGURATIONS: ranges, norms, activations, aliases AC/VP, CNL/NPHI)
    - models.py (MODEL_TEMPLATES + GWLT size configs)
    - training.py (TRAINING_TEMPLATES, DATA_TEMPLATES, INTEGRATED_TEMPLATES)
    - validation.py (validators + reports)
  - api/
    - predictor.py (GeneralWellLogPredictor: config/template-driven inference)
    - legacy.py (backward-compat API)
  - las_processor.py (LAS/HDF5 preprocessing for inference)
  - model.py (original MWLT_*: embedding/transformer/decoder with legacy Sigmoid)
  - utils.py (get_device, EarlyStopping, RMSE/R²)
  - predictor.py (VpPredictor convenience wrapper)

init_density_base/
- core/
  - model.py (MWLT_*; Decoder without forced Sigmoid; position encoding; attention blocks)
  - dataset.py (WellDataset: simple HDF5 slicing + random crop)
  - utils.py (robust save_checkpoint/load_checkpoint, EarlyStopping, metrics)
- density_prediction_improved.py
  - ImprovedDensityPipeline: dataset+normalizer (inline), DensityLoss, training & evaluation
- density_prediction_plot_improved.py (visualization)
- archives/*.md (implementation notes, changelogs)

Root files
- 1_Generalization_Architecture.md (how we consolidate into init_generalization/; file mapping + steps)
- README.md (project overview and quick starts)

Planned (not yet created)
- init_generalization/ (new consolidated package)
  - core/model: transformer.py, decoder.py
  - core/data: dataset.py, normalizer.py, io_las.py
  - core/training: manager.py, losses.py, metrics.py, early_stopping.py, checkpoint.py
  - core/configs: curves.py, models.py, training.py, validation.py
  - api: predictor.py (+ thin vp/density wrappers)
  - pipelines: train.py, evaluate.py, infer.py
  - tests: unit + tiny end-to-end

Common entry points
- Vp training/testing (examples):
  - init_vp_pred/train_vp_improved.py
  - init_vp_pred/test_vp_pipeline.py
- Density improved pipeline:
  - init_density_base/density_prediction_improved.py

What to search for (handy grep patterns)
- Backbone classes: "class GeneralWellLogTransformer|class MWL_Transformer"
- Decoder variants: "class GeneralDecoder|class VpCompatibleDecoder|class MultiCurveDecoder|class Decoder"
- Dataset: "class GeneralWellLogDataset|class WellDataset"
- Normalizer: "class GeneralDataNormalizer|class VpCompatibleNormalizer|DensityDataNormalizer"
- Losses: "class GeneralWellLogLoss|CurveSpecificLossFactory|class DensityLoss"
- Trainer: "class GeneralTrainingManager|EarlyStopping"
- Configs: "CURVE_CONFIGURATIONS|MODEL_TEMPLATES|TRAINING_TEMPLATES|INTEGRATED_TEMPLATES"

Notes
- Sequence convention: training windows typically total_length=720, effective_length=640 (crop/overlap)
- For resistivity (RLLD/RILD), normalization uses log10 preprocessing in configs/curves.py
- Physics constraints are applied in loss functions (per-curve ranges)
- Robust checkpoint load/save logic currently lives in init_density_base/core/utils.py and will be reused in the generalized package

Next steps (from the plan)
- Create init_generalization/ and copy modules per 1_Generalization_Architecture.md
- Update imports, standardize checkpointing, and add CLI pipelines
- Add tiny CPU e2e tests for AC and DEN to validate the refactor

