"""
Model template configurations for General Well Log Transformer

Predefined configurations for common well log prediction scenarios,
maintaining backward compatibility with VpTransformer while enabling
new multi-curve capabilities.
"""

MODEL_TEMPLATES = {
    # Backward compatible Vp prediction (original VpTransformer)
    'vp_prediction': {
        'name': 'Sonic Velocity Prediction',
        'description': 'Predict sonic velocity (Vp) from conventional logs - backward compatible',
        'input_curves': ['GR', 'CNL', 'DEN', 'RLLD'],
        'output_curves': ['VP'],
        'model_config': {
            'architecture': 'transformer',
            'model_size': 'base',
            'res_blocks': 4,
            'encoder_layers': 4,
            'attention_heads': 4,
            'feature_dim': 64,
            'sequence_length': 640
        },
        'training_config': {
            'optimizer': 'adam',
            'learning_rate': 1e-4,
            'batch_size': 8,
            'max_epochs': 200,
            'early_stopping_patience': 50,
            'loss_weights': {'VP': 1.0}
        },
        'data_config': {
            'validation_split': 0.2,
            'augmentation': True,
            'total_sequence_length': 720,
            'effective_sequence_length': 640
        },
        'compatibility': {
            'vp_transformer': True,
            'legacy_api': True
        }
    },
    
    # Density prediction (alternative configuration)
    'density_prediction': {
        'name': 'Bulk Density Prediction',
        'description': 'Predict bulk density from other petrophysical logs',
        'input_curves': ['GR', 'CNL', 'AC', 'RLLD'],
        'output_curves': ['DEN'],
        'model_config': {
            'architecture': 'transformer',
            'model_size': 'base',
            'res_blocks': 4,
            'encoder_layers': 4,
            'attention_heads': 4,
            'feature_dim': 64,
            'sequence_length': 640
        },
        'training_config': {
            'optimizer': 'adam',
            'learning_rate': 5e-5,  # Slightly lower for density
            'batch_size': 16,
            'max_epochs': 300,
            'early_stopping_patience': 75,
            'loss_weights': {'DEN': 1.0}
        },
        'data_config': {
            'validation_split': 0.2,
            'augmentation': True,
            'total_sequence_length': 720,
            'effective_sequence_length': 640
        },
        'compatibility': {
            'vp_transformer': False,
            'legacy_api': False
        }
    },
    
    # Multi-curve prediction
    'multi_curve_basic': {
        'name': 'Multi-Curve Basic Prediction',
        'description': 'Predict multiple curves from basic log suite',
        'input_curves': ['GR', 'CNL'],
        'output_curves': ['DEN', 'AC', 'RLLD'],
        'model_config': {
            'architecture': 'transformer',
            'model_size': 'large',  # Larger model for multi-output
            'res_blocks': 6,
            'encoder_layers': 6,
            'attention_heads': 8,
            'feature_dim': 128,
            'sequence_length': 640
        },
        'training_config': {
            'optimizer': 'adamw',
            'learning_rate': 3e-5,
            'batch_size': 4,  # Smaller batch for memory
            'max_epochs': 500,
            'early_stopping_patience': 100,
            'loss_weights': {'DEN': 1.0, 'AC': 0.8, 'RLLD': 0.6}  # Weighted by importance
        },
        'data_config': {
            'validation_split': 0.25,  # More validation data for multi-output
            'augmentation': True,
            'total_sequence_length': 720,
            'effective_sequence_length': 640
        },
        'compatibility': {
            'vp_transformer': False,
            'legacy_api': False
        }
    },
    
    # Missing section filling
    'missing_section_fill': {
        'name': 'Missing Section Filling',
        'description': 'Fill missing log sections using available curves',
        'input_curves': ['GR', 'DEN', 'AC'],
        'output_curves': ['CNL', 'RLLD'],
        'model_config': {
            'architecture': 'transformer',
            'model_size': 'base',
            'res_blocks': 4,
            'encoder_layers': 4,
            'attention_heads': 4,
            'feature_dim': 64,
            'sequence_length': 640
        },
        'training_config': {
            'optimizer': 'adam',
            'learning_rate': 8e-5,
            'batch_size': 12,
            'max_epochs': 250,
            'early_stopping_patience': 60,
            'loss_weights': {'CNL': 1.0, 'RLLD': 1.0}
        },
        'data_config': {
            'validation_split': 0.2,
            'augmentation': True,
            'total_sequence_length': 720,
            'effective_sequence_length': 640,
            'missing_data_simulation': True  # Train with simulated missing sections
        },
        'compatibility': {
            'vp_transformer': False,
            'legacy_api': False
        }
    },
    
    # Comprehensive prediction (all common curves)
    'comprehensive_logs': {
        'name': 'Comprehensive Log Prediction',
        'description': 'Predict comprehensive log suite from minimal inputs',
        'input_curves': ['GR', 'SP'],  # Minimal input set
        'output_curves': ['DEN', 'CNL', 'AC', 'RLLD', 'PE'],
        'model_config': {
            'architecture': 'transformer',
            'model_size': 'large',
            'res_blocks': 8,  # Larger model for complex task
            'encoder_layers': 8,
            'attention_heads': 8,
            'feature_dim': 128,
            'sequence_length': 640
        },
        'training_config': {
            'optimizer': 'adamw',
            'learning_rate': 2e-5,  # Conservative learning rate
            'batch_size': 2,  # Very small batch for memory
            'max_epochs': 800,
            'early_stopping_patience': 150,
            'loss_weights': {
                'DEN': 1.0, 'CNL': 0.9, 'AC': 0.8, 
                'RLLD': 0.7, 'PE': 0.6
            }
        },
        'data_config': {
            'validation_split': 0.3,  # More validation for complex model
            'augmentation': True,
            'total_sequence_length': 720,
            'effective_sequence_length': 640,
            'noise_augmentation': True
        },
        'compatibility': {
            'vp_transformer': False,
            'legacy_api': False
        }
    },
    
    # Fast inference template (optimized for speed)
    'fast_inference': {
        'name': 'Fast Inference Model',
        'description': 'Optimized for fast inference with acceptable accuracy',
        'input_curves': ['GR', 'DEN'],
        'output_curves': ['AC'],
        'model_config': {
            'architecture': 'transformer',
            'model_size': 'small',  # Small for speed
            'res_blocks': 2,
            'encoder_layers': 2,
            'attention_heads': 2,
            'feature_dim': 32,  # Reduced features
            'sequence_length': 320  # Shorter sequences
        },
        'training_config': {
            'optimizer': 'adam',
            'learning_rate': 2e-4,  # Higher learning rate for fast training
            'batch_size': 32,  # Large batch size
            'max_epochs': 100,
            'early_stopping_patience': 25,
            'loss_weights': {'AC': 1.0}
        },
        'data_config': {
            'validation_split': 0.15,
            'augmentation': False,  # No augmentation for speed
            'total_sequence_length': 360,
            'effective_sequence_length': 320
        },
        'compatibility': {
            'vp_transformer': False,
            'legacy_api': False
        }
    }
}

# Model size configurations
MODEL_SIZE_CONFIGS = {
    'small': {
        'res_blocks': 2,
        'encoder_layers': 2,
        'attention_heads': 2,
        'feature_dim': 64,
        'parameters': '~1.2M'
    },
    'base': {
        'res_blocks': 4,
        'encoder_layers': 4,
        'attention_heads': 4,
        'feature_dim': 64,
        'parameters': '~2.1M'
    },
    'large': {
        'res_blocks': 6,
        'encoder_layers': 6,
        'attention_heads': 8,
        'feature_dim': 128,
        'parameters': '~8.4M'
    }
}

def get_model_template(template_name):
    """
    Get a model template configuration
    
    Args:
        template_name: Name of the template
        
    Returns:
        dict: Deep copy of template configuration
        
    Raises:
        ValueError: If template not found
    """
    if template_name not in MODEL_TEMPLATES:
        available = list(MODEL_TEMPLATES.keys())
        raise ValueError(f"Template '{template_name}' not found. "
                        f"Available templates: {available}")
    
    import copy
    return copy.deepcopy(MODEL_TEMPLATES[template_name])

def get_available_templates():
    """Get list of all available template names"""
    return list(MODEL_TEMPLATES.keys())

def get_templates_by_compatibility(legacy_compatible=True):
    """
    Get templates by compatibility requirements
    
    Args:
        legacy_compatible: If True, return only legacy-compatible templates
        
    Returns:
        list: Template names matching criteria
    """
    return [name for name, template in MODEL_TEMPLATES.items()
            if template['compatibility']['legacy_api'] == legacy_compatible]

def create_custom_template(name, input_curves, output_curves, 
                          model_size='base', **kwargs):
    """
    Create a custom model template
    
    Args:
        name: Template name
        input_curves: List of input curve names
        output_curves: List of output curve names
        model_size: 'small', 'base', or 'large'
        **kwargs: Override any template parameters
        
    Returns:
        dict: Custom template configuration
    """
    # Get base configuration from model size
    size_config = MODEL_SIZE_CONFIGS[model_size]
    
    # Create base template
    template = {
        'name': name,
        'description': f'Custom template: {input_curves} -> {output_curves}',
        'input_curves': input_curves,
        'output_curves': output_curves,
        'model_config': {
            'architecture': 'transformer',
            'model_size': model_size,
            **size_config,
            'sequence_length': 640
        },
        'training_config': {
            'optimizer': 'adam',
            'learning_rate': 1e-4,
            'batch_size': 8,
            'max_epochs': 200,
            'early_stopping_patience': 50,
            'loss_weights': {curve: 1.0 for curve in output_curves}
        },
        'data_config': {
            'validation_split': 0.2,
            'augmentation': True,
            'total_sequence_length': 720,
            'effective_sequence_length': 640
        },
        'compatibility': {
            'vp_transformer': False,
            'legacy_api': False
        }
    }
    
    # Apply any overrides
    def deep_update(base_dict, update_dict):
        for key, value in update_dict.items():
            if isinstance(value, dict) and key in base_dict:
                deep_update(base_dict[key], value)
            else:
                base_dict[key] = value
    
    deep_update(template, kwargs)
    return template

def validate_template_config(template_config):
    """
    Validate a template configuration for completeness and consistency
    
    Args:
        template_config: Template configuration dict
        
    Returns:
        tuple: (is_valid, validation_messages)
    """
    messages = []
    is_valid = True
    
    # Check required fields
    required_fields = ['name', 'input_curves', 'output_curves', 
                      'model_config', 'training_config', 'data_config']
    
    for field in required_fields:
        if field not in template_config:
            messages.append(f"Missing required field: {field}")
            is_valid = False
    
    if not is_valid:
        return is_valid, messages
    
    # Validate curves exist in configuration
    from .curves import CURVE_CONFIGURATIONS
    
    all_curves = template_config['input_curves'] + template_config['output_curves']
    for curve in all_curves:
        if curve.upper() not in CURVE_CONFIGURATIONS:
            messages.append(f"Unknown curve: {curve}")
            is_valid = False
    
    # Check model configuration consistency
    model_config = template_config['model_config']
    if 'model_size' in model_config:
        size = model_config['model_size']
        if size not in MODEL_SIZE_CONFIGS:
            messages.append(f"Unknown model size: {size}")
            is_valid = False
    
    # Validate output curve count matches loss weights
    output_curves = template_config['output_curves']
    loss_weights = template_config['training_config'].get('loss_weights', {})
    
    for curve in output_curves:
        if curve not in loss_weights:
            messages.append(f"Missing loss weight for output curve: {curve}")
    
    return is_valid, messages