"""
Training manager module for General Well Log Transformer - import wrapper

This module imports the unchanged GeneralTrainingManager from the original
vp_predictor codebase to avoid duplication while maintaining the specialized
directory structure for vpvsdens.
"""

# Import the unchanged training manager from original codebase
from ...init_vp_pred.vp_predictor.core.training import GeneralTrainingManager

# Re-export for convenience
__all__ = ['GeneralTrainingManager']
