# Initial Density Prediction Module

This is a self-contained density prediction module that consolidates all components of the MWLT Transformer density prediction system. The module has been designed to operate independently with all required dependencies included.

## 📁 Module Contents

### Main Pipeline Files
- **`density_prediction_improved.py`** - Main improved density prediction module with enhanced normalization
- **`density_prediction_plot_improved.py`** - Comprehensive plotting and visualization capabilities

### Core Support Files (`core/` directory)
- **`core/model.py`** - MWLT Transformer architecture (Small, Base, Large variants)
- **`core/utils.py`** - Utility functions for device detection, checkpoints, and metrics
- **`core/dataset.py`** - Original dataset class for compatibility
- **`core/__init__.py`** - Core module initialization

### Archived Files (`archives/` directory)
- **Legacy Scripts**: `train.py`, `test.py`, `density_prediction_pipeline.py`, `plot_density_results.py`
- **Test Scripts**: `verify_ml_architecture.py`, `test_checkpoint_fix.py`, `test_local_save_path.py`
- **Examples/Setup**: `example_usage.py`, `setup.py`

## 🚀 Quick Start

### 1. Basic Usage with Improved Pipeline

```python
from density_prediction_improved import DensityDataNormalizer, ImprovedDensityDataset, create_density_dataset
from density_prediction_plot_improved import DensityPlotterImproved
from core.model import MWLT_Small, MWLT_Base, MWLT_Large
from core.utils import get_device, load_checkpoint

# Create dataset
dataset = create_density_dataset(
    file_path="path/to/your/data.hdf5",
    input_curves=['GR', 'AC', 'CNL', 'RLLD'],
    output_curves=['DENSITY'],
    sequence_length=640,
    augment=True
)

# Create and train model
device = get_device()
model = MWLT_Small(in_channels=4, out_channels=1, seq_len=640).to(device)

# Generate comprehensive plots
plotter = DensityPlotterImproved("results_directory")
plotter.create_comprehensive_report(
    model_path="path/to/model.pth",
    test_file="path/to/test_data.hdf5",
    input_curves=['GR', 'AC', 'CNL', 'RLLD'],
    output_curves=['DENSITY']
)
```

### 2. Using the Integrated Pipeline

```bash
python density_prediction_pipeline.py --model_type base --epochs 100 --batch_size 32
```

### 3. Plotting Only

```bash
python density_prediction_plot_improved.py --results_dir ../results --test_file ../data.hdf5
```

## 🔧 Key Features

### Enhanced Normalization
- **DensityDataNormalizer**: Proper curve-specific normalization
- **Physical Range Validation**: Ensures density predictions stay within realistic bounds (1.5-3.0 g/cc)
- **Robust Input Handling**: Handles missing curves and alternative naming conventions

### Improved Dataset Handling
- **ImprovedDensityDataset**: Enhanced dataset class with better error handling
- **Flexible Input/Output**: Supports various curve combinations
- **Data Augmentation**: Optional random cropping for training

### Comprehensive Visualization
- **Training History**: Loss curves and convergence analysis
- **Prediction Results**: Scatter plots, residuals, error distributions
- **Error Analysis**: Detailed statistical analysis and Q-Q plots
- **Input Curves Analysis**: Visualization of input well log curves

### Model Architectures
- **MWLT_Small**: Lightweight model for quick training and testing
- **MWLT_Base**: Standard model with good performance/speed balance
- **MWLT_Large**: High-capacity model for maximum accuracy

## 📊 Data Requirements

### Input Data Format
- **File Type**: HDF5 (.hdf5, .h5)
- **Curve Names**: Flexible naming (GR, AC, CNL, RLLD, DEN/DENSITY/RHOB)
- **Sequence Length**: Default 640 points (from 720 total)
- **Data Shape**: Each curve should be 1D array of length 720

### Supported Input Curves
- **GR**: Gamma Ray (API units)
- **AC**: Acoustic Transit Time (μs/ft)
- **CNL**: Compensated Neutron Log (%)
- **RLLD**: Deep Resistivity (ohm-m)

### Output Curves
- **DENSITY/DEN/RHOB**: Bulk Density (g/cc)

## 🎯 Model Performance

### Expected Metrics
- **RMSE**: < 0.1 g/cc for good models
- **R²**: > 0.85 for acceptable performance
- **MAE**: < 0.08 g/cc for production use

### Training Tips
1. **Data Quality**: Ensure input curves are properly quality-controlled
2. **Normalization**: Use the improved normalizer for best results
3. **Sequence Length**: 640 points provides good context for most wells
4. **Augmentation**: Enable for training, disable for inference
5. **Early Stopping**: Use patience=10-20 to prevent overfitting

## 🛠️ Dependencies

### Required Python Packages
```
torch>=1.9.0
numpy>=1.20.0
h5py>=3.0.0
matplotlib>=3.3.0
seaborn>=0.11.0
pandas>=1.3.0
scikit-learn>=0.24.0
```

### Hardware Requirements
- **GPU**: Recommended (CUDA-compatible)
- **CPU**: Supported as fallback
- **Memory**: 8GB+ RAM recommended
- **Storage**: 1GB+ for models and results

## 📈 Usage Examples

### Training a New Model
```python
# Using the pipeline
python density_prediction_pipeline.py \
    --model_type base \
    --epochs 200 \
    --batch_size 32 \
    --save_path ./results \
    --input_curves GR AC CNL RLLD \
    --output_curves DENSITY
```

### Evaluating an Existing Model
```python
# Using the plotting script
python density_prediction_plot_improved.py \
    --results_dir ./results \
    --test_file ./test_data.hdf5 \
    --model_type base \
    --plot_type all
```

### Custom Training Script
```python
from density_prediction_improved import DensityDataNormalizer, ImprovedDensityDataset
from model import MWLT_Base
from utils import get_device, save_checkpoint, EarlyStopping
import torch.optim as optim

# Setup
device = get_device()
normalizer = DensityDataNormalizer()

# Create dataset
dataset = ImprovedDensityDataset(
    file_path="training_data.hdf5",
    input_curves=['GR', 'AC', 'CNL', 'RLLD'],
    output_curves=['DENSITY'],
    normalizer=normalizer,
    sequence_length=640,
    augment=True
)

# Create model
model = MWLT_Base(in_channels=4, out_channels=1, seq_len=640).to(device)
optimizer = optim.Adam(model.parameters(), lr=0.001)
criterion = torch.nn.MSELoss()

# Training loop (simplified)
for epoch in range(100):
    for inputs, targets in DataLoader(dataset, batch_size=32):
        inputs, targets = inputs.to(device), targets.to(device)
        
        optimizer.zero_grad()
        outputs = model(inputs)
        loss = criterion(outputs, targets)
        loss.backward()
        optimizer.step()
```

## 🔍 Troubleshooting

### Common Issues
1. **Missing Curves**: Check curve names in HDF5 file
2. **Memory Errors**: Reduce batch size or sequence length
3. **Poor Performance**: Verify data quality and normalization
4. **Import Errors**: Ensure all dependencies are installed

### Debug Mode
Enable verbose output by setting environment variable:
```bash
export DENSITY_DEBUG=1
python your_script.py
```

## 📝 License and Attribution

This module is part of the MWLT (Multi-Well Log Transformer) project. Please cite appropriately when using in research or commercial applications.

---

For more detailed information, see the individual script documentation and inline comments.
