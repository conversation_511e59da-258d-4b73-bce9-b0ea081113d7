"""
VP Prediction Visualization Module
Contains all plotting functions for VP prediction pipeline results

Author: Refactored for MWLT System
Date: 2025-09-04
"""
import json
import numpy as np
import matplotlib
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
from typing import Dict, List, Any, Tuple
from scipy import stats
from sklearn.metrics import r2_score, mean_absolute_error, mean_squared_error


def create_training_plots(history: Dict[str, List[float]], 
                         output_dir: Path, 
                         config: Dict[str, Any] = None,
                         interactive_plots: bool = False):
    """
    Create training visualization plots
    
    Args:
        history: Training history dictionary with loss and metrics
        output_dir: Directory to save plots
        config: Configuration dictionary
        interactive_plots: Whether to display plots interactively
    """
    if not history.get('train_loss'):
        return

    plt.style.use('default')
    fig, axes = plt.subplots(2, 2, figsize=(16, 10), constrained_layout=True)
    fig.suptitle('VP Model Training Progress', fontsize=16, fontweight='bold', y=0.98)

    epochs = range(1, len(history['train_loss']) + 1)

    # Loss plot
    axes[0, 0].plot(epochs, history['train_loss'], 'b-', label='Training Loss', linewidth=2)
    axes[0, 0].plot(epochs, history['val_loss'], 'r-', label='Validation Loss', linewidth=2)
    axes[0, 0].set_title('Training and Validation Loss')
    axes[0, 0].set_xlabel('Epoch')
    axes[0, 0].set_ylabel('Loss')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)

    # R² plot
    axes[0, 1].plot(epochs, history.get('val_r2', []), 'g-', label='Validation R²', linewidth=2)
    axes[0, 1].set_title('Validation R² Score')
    axes[0, 1].set_xlabel('Epoch')
    axes[0, 1].set_ylabel('R² Score')
    axes[0, 1].legend()
    axes[0, 1].grid(True, alpha=0.3)

    # RMSE plot
    axes[1, 0].plot(epochs, history.get('val_rmse', []), 'm-', label='Validation RMSE', linewidth=2)
    axes[1, 0].set_title('Validation RMSE')
    axes[1, 0].set_xlabel('Epoch')
    axes[1, 0].set_ylabel('RMSE')
    axes[1, 0].legend()
    axes[1, 0].grid(True, alpha=0.3)

    # Learning curve (combined)
    ax2 = axes[1, 1].twinx()
    axes[1, 1].plot(epochs, history.get('val_r2', []), 'g-', label='R²', linewidth=2)
    ax2.plot(epochs, history.get('val_rmse', []), 'm-', label='RMSE', linewidth=2)
    axes[1, 1].set_title('Combined Metrics')
    axes[1, 1].set_xlabel('Epoch')
    axes[1, 1].set_ylabel('R² Score', color='g')
    ax2.set_ylabel('RMSE', color='m')
    axes[1, 1].legend(loc='upper left')
    ax2.legend(loc='upper right')
    axes[1, 1].grid(True, alpha=0.3)

    # Save plot
    plot_file = output_dir / 'training_progress.png'
    fig.savefig(plot_file, dpi=300)

    # Display interactive plot if requested
    if interactive_plots:
        try:
            plt.show(block=False)  # Non-blocking show
        except Exception as e:
            print(f"Failed to display interactive plot: {e}")
    else:
        plt.close()
        
    return plot_file


def create_cv_plots(cv_results: List[Dict[str, float]], 
                   cv_summary: Dict[str, Any],
                   output_dir: Path,
                   interactive_plots: bool = False):
    """
    Create cross-validation visualization plots
    
    Args:
        cv_results: Cross-validation results for each fold
        cv_summary: Summary statistics of cross-validation
        output_dir: Directory to save plots
        interactive_plots: Whether to display plots interactively
    """
    plt.style.use('default')
    fig, axes = plt.subplots(2, 2, figsize=(16, 10), constrained_layout=True)
    fig.suptitle('Cross-Validation Results', fontsize=16, fontweight='bold', y=0.98)

    metrics = ['r2', 'rmse', 'mae', 'mse']
    titles = ['R² Score', 'RMSE', 'MAE', 'MSE']

    for idx, (metric, title) in enumerate(zip(metrics, titles)):
        ax = axes[idx // 2, idx % 2]
        values = [result[metric] for result in cv_results]

        # Box plot
        ax.boxplot(values, labels=[metric.upper()])
        ax.scatter(range(1, len(values) + 1), values, alpha=0.7, color='red')

        # Add mean line
        mean_val = cv_summary[metric]['mean']
        ax.axhline(y=mean_val, color='blue', linestyle='--', alpha=0.7, label=f'Mean: {mean_val:.4f}')

        ax.set_title(f'{title} Across Folds')
        ax.set_ylabel(title)
        ax.grid(True, alpha=0.3)
        ax.legend()

    # Save plot
    plot_file = output_dir / 'cv_results.png'
    fig.savefig(plot_file, dpi=300)

    # Display interactive plot if requested
    if interactive_plots:
        try:
            plt.show(block=False)  # Non-blocking show
        except Exception as e:
            print(f"Failed to display interactive plot: {e}")
    else:
        plt.close()
        
    return plot_file


def visualize_prediction_results(prediction_results: Dict[str, Any],
                                output_dir: Path,
                                interactive_plots: bool = False):
    """
    Create comprehensive prediction visualizations
    
    Args:
        prediction_results: Dictionary containing predictions, actuals, and metrics
        output_dir: Directory to save plots
        interactive_plots: Whether to display plots interactively
    """
    predictions = np.array(prediction_results['predictions'])
    actuals = np.array(prediction_results['actuals'])
    metrics = prediction_results['metrics']

    # Create comprehensive visualization
    plt.style.use('default')
    fig, axes = plt.subplots(2, 3, figsize=(20, 12), constrained_layout=True)
    fig.suptitle('VP Prediction Results Analysis', fontsize=16, fontweight='bold', y=0.98)

    # 1. Scatter plot (Predicted vs Actual)
    axes[0, 0].scatter(actuals, predictions, alpha=0.6, s=20)
    min_val, max_val = min(actuals.min(), predictions.min()), max(actuals.max(), predictions.max())
    axes[0, 0].plot([min_val, max_val], [min_val, max_val], 'r--', lw=2, label='Perfect Prediction')
    axes[0, 0].set_xlabel('Actual VP')
    axes[0, 0].set_ylabel('Predicted VP')
    axes[0, 0].set_title(f'Predicted vs Actual (R² = {metrics["r2"]:.4f})')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)

    # 2. Residuals plot
    residuals = predictions - actuals
    axes[0, 1].scatter(actuals, residuals, alpha=0.6, s=20)
    axes[0, 1].axhline(y=0, color='r', linestyle='--', lw=2)
    axes[0, 1].set_xlabel('Actual VP')
    axes[0, 1].set_ylabel('Residuals (Predicted - Actual)')
    axes[0, 1].set_title('Residuals Plot')
    axes[0, 1].grid(True, alpha=0.3)

    # 3. Histogram of residuals
    axes[0, 2].hist(residuals, bins=50, alpha=0.7, edgecolor='black')
    axes[0, 2].axvline(x=0, color='r', linestyle='--', lw=2)
    axes[0, 2].set_xlabel('Residuals')
    axes[0, 2].set_ylabel('Frequency')
    axes[0, 2].set_title('Distribution of Residuals')
    axes[0, 2].grid(True, alpha=0.3)

    # 4. Time series plot (first 1000 points)
    n_points = min(1000, len(predictions))
    indices = range(n_points)
    axes[1, 0].plot(indices, actuals[:n_points], 'b-', label='Actual', alpha=0.7, linewidth=1)
    axes[1, 0].plot(indices, predictions[:n_points], 'r-', label='Predicted', alpha=0.7, linewidth=1)
    axes[1, 0].set_xlabel('Sample Index')
    axes[1, 0].set_ylabel('VP Value')
    axes[1, 0].set_title(f'Time Series Comparison (First {n_points} samples)')
    axes[1, 0].legend()
    axes[1, 0].grid(True, alpha=0.3)

    # 5. Error distribution
    abs_errors = np.abs(residuals)
    axes[1, 1].hist(abs_errors, bins=50, alpha=0.7, edgecolor='black')
    axes[1, 1].set_xlabel('Absolute Error')
    axes[1, 1].set_ylabel('Frequency')
    axes[1, 1].set_title(f'Absolute Error Distribution (MAE = {metrics["mae"]:.2f})')
    axes[1, 1].grid(True, alpha=0.3)

    # 6. Metrics summary
    axes[1, 2].axis('off')
    metrics_text = f"""
    Prediction Metrics:

    R² Score: {metrics['r2']:.4f}
    RMSE: {metrics['rmse']:.2f}
    MAE: {metrics['mae']:.2f}
    MSE: {metrics['mse']:.2f}

    Data Points: {len(predictions):,}

    Performance Assessment:
    {'Excellent' if metrics['r2'] >= 0.8 else 'Good' if metrics['r2'] >= 0.6 else 'Fair' if metrics['r2'] >= 0.4 else 'Poor'}
    """
    axes[1, 2].text(0.1, 0.5, metrics_text, fontsize=12, verticalalignment='center',
                    bbox=dict(boxstyle="round,pad=0.3", facecolor="lightblue", alpha=0.7))

    # Save plot
    plot_file = output_dir / 'prediction_results.png'
    fig.savefig(plot_file, dpi=300)

    # Display interactive plot if requested
    if interactive_plots:
        try:
            plt.show(block=False)  # Non-blocking show
        except Exception as e:
            print(f"Failed to display interactive plot: {e}")
    else:
        plt.close()
        
    return plot_file


def load_and_visualize_training_results(training_dir: str, interactive_plots: bool = False):
    """
    Load training results and create visualizations
    
    Args:
        training_dir: Path to training directory
        interactive_plots: Whether to display plots interactively
    """
    training_path = Path(training_dir)
    
    # Load training history
    history_file = training_path / 'training_history.json'
    if not history_file.exists():
        raise FileNotFoundError(f"Training history file not found: {history_file}")
    
    with open(history_file, 'r') as f:
        history = json.load(f)
    
    # Create plots
    plot_file = create_training_plots(history, training_path, interactive_plots=interactive_plots)
    print(f"Training plots saved to {plot_file}")
    return plot_file


def load_and_visualize_cv_results(validation_dir: str, interactive_plots: bool = False):
    """
    Load cross-validation results and create visualizations
    
    Args:
        validation_dir: Path to validation directory
        interactive_plots: Whether to display plots interactively
    """
    validation_path = Path(validation_dir)
    
    # Load CV results
    results_file = validation_path / 'cv_results.json'
    if not results_file.exists():
        raise FileNotFoundError(f"CV results file not found: {results_file}")
    
    with open(results_file, 'r') as f:
        cv_data = json.load(f)
    
    cv_results = cv_data['cv_results']
    cv_summary = cv_data['cv_summary']
    
    # Create plots
    plot_file = create_cv_plots(cv_results, cv_summary, validation_path, interactive_plots=interactive_plots)
    print(f"Cross-validation plots saved to {plot_file}")
    return plot_file


def load_and_visualize_prediction_results(prediction_dir: str, interactive_plots: bool = False):
    """
    Load prediction results and create visualizations
    
    Args:
        prediction_dir: Path to prediction directory
        interactive_plots: Whether to display plots interactively
    """
    prediction_path = Path(prediction_dir)
    
    # Load prediction results
    results_file = prediction_path / 'detailed_predictions.json'
    if not results_file.exists():
        # Try alternative file name
        results_file = prediction_path / 'prediction_results.json'
        if not results_file.exists():
            raise FileNotFoundError(f"Prediction results file not found in {prediction_path}")
    
    with open(results_file, 'r') as f:
        pred_data = json.load(f)
    
    # Format data for visualization
    if 'predictions' in pred_data and 'actuals' in pred_data:
        # Already in the right format
        prediction_results = pred_data
    else:
        # Need to reformat
        prediction_results = {
            'predictions': np.array(pred_data.get('predictions', [])),
            'actuals': np.array(pred_data.get('actuals', [])),
            'metrics': pred_data.get('metrics', {})
        }
    
    # Calculate metrics if not provided
    if not prediction_results['metrics']:
        predictions = np.array(prediction_results['predictions'])
        actuals = np.array(prediction_results['actuals'])
        prediction_results['metrics'] = {
            'r2': r2_score(actuals, predictions),
            'rmse': np.sqrt(mean_squared_error(actuals, predictions)),
            'mae': mean_absolute_error(actuals, predictions),
            'mse': mean_squared_error(actuals, predictions)
        }
    
    # Create plots
    plot_file = visualize_prediction_results(prediction_results, prediction_path, interactive_plots=interactive_plots)
    print(f"Prediction plots saved to {plot_file}")
    return plot_file


# Utility function to set up matplotlib for interactive plots
def setup_interactive_plots():
    """Set up matplotlib for interactive plotting"""
    try:
        matplotlib.use('TkAgg')  # Try TkAgg first
        print("Using TkAgg backend for interactive plots")
    except ImportError:
        try:
            matplotlib.use('Qt5Agg')  # Try Qt5Agg as fallback
            print("Using Qt5Agg backend for interactive plots")
        except ImportError:
            try:
                matplotlib.use('Qt4Agg')  # Try Qt4Agg as fallback
                print("Using Qt4Agg backend for interactive plots")
            except ImportError:
                print("No interactive backend available, plots will be saved but not displayed")
                return False
    plt.ion()  # Turn on interactive mode
    return True


def keep_plots_open():
    """Keep matplotlib plots open until user closes them"""
    try:
        print("\n⏸️  Plots are now displayed. Close the plot windows when you're done viewing them.")
        print("   Or press Ctrl+C here to close all plots and exit.")
        plt.show(block=True)  # Block until all plots are closed
    except KeyboardInterrupt:
        print("\n⏹️  Closing all plots...")
        plt.close('all')
    except Exception as e:
        print(f"\n⚠️  Error keeping plots open: {e}")
        try:
            input("\nPress Enter to continue...")
        except:
            pass
def display_saved_plots(output_dir: Path, plot_filenames: List[str] = None, interactive: bool = True):
    """
    Load and display saved PNG plot files from the output directory.
    
    Args:
        output_dir: Directory containing the saved PNG files
        plot_filenames: Specific list of PNG filenames to display (e.g., ['training_progress.png']). 
                        If None, displays all .png files in the directory.
        interactive: Whether to display interactively using plt.show()
    """
    import glob
    plt.style.use('default')
    
    if plot_filenames is None:
        # Find all PNG files in the directory
        plot_files = list(output_dir.glob('*.png'))
    else:
        plot_files = [output_dir / fname for fname in plot_filenames if (output_dir / fname).exists()]
    
    if not plot_files:
        print(f"No PNG files found in {output_dir}")
        return
    
    print(f"Found {len(plot_files)} plot files to display:")
    for plot_file in plot_files:
        print(f"  - {plot_file.name}")
    
    for plot_file in plot_files:
        try:
            # Load the PNG image
            image = plt.imread(plot_file)
            
            # Create a figure and display the image
            fig, ax = plt.subplots(figsize=(12, 8))
            ax.imshow(image)
            ax.set_title(f'{plot_file.stem}', fontsize=14, fontweight='bold')
            ax.axis('off')  # Hide axes for clean display
            
            # Add file info
            fig.text(0.5, 0.02, f'File: {plot_file.name} | Directory: {output_dir}', 
                     ha='center', fontsize=10, style='italic')
            
            fig.tight_layout(pad=1.2)
            
            if interactive:
                plt.show(block=False)  # Non-blocking show
            else:
                # Save a copy or just close
                plt.close(fig)
                
        except Exception as e:
            print(f"Failed to display {plot_file}: {e}")
    
    print("Plot display completed.")


# Example usage for loading and displaying saved plots
def load_and_display_all_results(training_dir: str, 
                                validation_dir: str = None, 
                                prediction_dir: str = None,
                                interactive_plots: bool = False):
    """
    Load and display all saved plot results from training, validation, and prediction directories.
    
    Args:
        training_dir: Path to training directory
        validation_dir: Path to validation directory (optional)
        prediction_dir: Path to prediction directory (optional)
        interactive_plots: Whether to display plots interactively
    """
    training_path = Path(training_dir)
    plot_file = None
    
    # Display training plots
    if training_path.exists():
        print("\n📊 Displaying Training Plots...")
        display_saved_plots(training_path, interactive=interactive_plots)
    
    # Display validation plots
    if validation_dir and Path(validation_dir).exists():
        print("\n📊 Displaying Validation Plots...")
        display_saved_plots(Path(validation_dir), interactive=interactive_plots)
    
    # Display prediction plots
    if prediction_dir and Path(prediction_dir).exists():
        print("\n📊 Displaying Prediction Plots...")
        display_saved_plots(Path(prediction_dir), interactive=interactive_plots)
    
    return "All saved plots displayed successfully."


def main():
    """
    Simple main function that automatically finds and displays all VP prediction results
    """
    print("🎯 VP Prediction Results Viewer")
    print("=" * 50)
    
    # Setup interactive plotting
    try:
        setup_interactive_plots()
        print("✅ Interactive plotting enabled")
    except Exception as e:
        print(f"⚠️  Interactive plotting setup failed: {e}")
        print("📁 Plots will be saved to files only")
    
    # Check if vp_prediction_outputs exists
    output_base = Path("vp_prediction_outputs")
    if not output_base.exists():
        print(f"❌ Directory not found: {output_base}")
        print("💡 Run the training pipeline first: python train_vp_improved.py")
        return
    
    print(f"📂 Searching for results in: {output_base}")
    
    # Find available result directories
    result_dirs = {
        'training': output_base / 'training',
        'validation': output_base / 'validation', 
        'prediction': output_base / 'prediction'
    }
    
    found_results = []
    for name, path in result_dirs.items():
        if path.exists():
            found_results.append((name, path))
            print(f"✅ Found {name} results: {path}")
        else:
            print(f"⚠️  No {name} results found")
    
    if not found_results:
        print("❌ No results found in any directory")
        return
    
    print("\n🎨 Loading and displaying plots...")
    print("=" * 50)
    
    # Display results for each found directory
    for name, path in found_results:
        print(f"\n📊 Displaying {name.upper()} plots...")
        
        try:
            if name == 'training':
                # Check if history file exists
                history_file = path / 'training_history.json'
                if history_file.exists():
                    plot_file = load_and_visualize_training_results(str(path), interactive_plots=True)
                    print(f"   ✅ Training plots: {plot_file}")
                else:
                    # Try to display existing PNG files
                    display_saved_plots(path, interactive=True)
                    
            elif name == 'validation':
                # Check if CV results exist
                cv_file = path / 'cv_results.json'
                if cv_file.exists():
                    plot_file = load_and_visualize_cv_results(str(path), interactive_plots=True)
                    print(f"   ✅ Validation plots: {plot_file}")
                else:
                    display_saved_plots(path, interactive=True)
                    
            elif name == 'prediction':
                # Check if prediction results exist
                pred_file = path / 'detailed_predictions.json'
                if pred_file.exists():
                    plot_file = load_and_visualize_prediction_results(str(path), interactive_plots=True)
                    print(f"   ✅ Prediction plots: {plot_file}")
                else:
                    display_saved_plots(path, interactive=True)
                    
        except Exception as e:
            print(f"   ❌ Failed to display {name} plots: {e}")
            # Fallback: try to display any PNG files
            try:
                display_saved_plots(path, interactive=True)
            except Exception as fallback_e:
                print(f"   ❌ Fallback display also failed: {fallback_e}")
    
    print("\n🎉 Plot display completed!")
    
    # Keep plots open if any were displayed
    if found_results:
        keep_plots_open()
    
    print("\n💡 PNG files are also saved in:")
    for name, path in found_results:
        png_files = list(path.glob('*.png'))
        if png_files:
            print(f"   📁 {path}: {len(png_files)} PNG files")


if __name__ == "__main__":
    main()
