"""
Initial Density Prediction Module

A self-contained density prediction module that consolidates all components 
of the MWLT Transformer density prediction system.

This module provides:
- Enhanced density prediction with proper normalization
- Comprehensive visualization and plotting capabilities
- Multiple model architectures (Small, Base, Large)
- Integrated training and testing pipelines

Main Classes:
    DensityDataNormalizer: Handles proper normalization for density prediction
    ImprovedDensityDataset: Enhanced dataset class for density prediction
    DensityPlotterImproved: Comprehensive plotting and visualization
    MWLT_Small, MWLT_Base, MWLT_Large: Model architectures

Example Usage:
    >>> from initial_density import DensityDataNormalizer, ImprovedDensityDataset
    >>> from initial_density import MWLT_Small, DensityPlotterImproved
    >>> 
    >>> # Create dataset
    >>> normalizer = DensityDataNormalizer()
    >>> dataset = ImprovedDensityDataset(
    ...     file_path="data.hdf5",
    ...     input_curves=['GR', 'AC', 'CNL', 'RLLD'],
    ...     output_curves=['DENSITY'],
    ...     normalizer=normalizer
    ... )
    >>> 
    >>> # Create model
    >>> model = MWLT_Small(in_channels=4, out_channels=1, seq_len=640)
    >>> 
    >>> # Create plotter
    >>> plotter = DensityPlotterImproved("results_dir")
"""

__version__ = "1.0.0"
__author__ = "MWLT Team"
__email__ = "<EMAIL>"

# Import main classes and functions
try:
    from model import (
        MWLT_Small,
        MWLT_Base,
        MWLT_Large,
        MWL_Transformer
    )

    from utils import (
        get_device,
        save_checkpoint,
        load_checkpoint,
        EarlyStopping,
        cal_RMSE,
        cal_R2
    )

    from dataset import WellDataset

except ImportError as e:
    print(f"Warning: Some imports failed in core module: {e}")
    print("This may be due to missing dependencies. Please ensure all required packages are installed.")

# Define what gets imported with "from core import *"
__all__ = [
    # Model architectures
    'MWLT_Small',
    'MWLT_Base',
    'MWLT_Large',
    'MWL_Transformer',

    # Utility functions
    'get_device',
    'save_checkpoint',
    'load_checkpoint',
    'EarlyStopping',
    'cal_RMSE',
    'cal_R2',

    # Dataset classes
    'WellDataset',
]

# Module metadata
__title__ = "Initial Density Prediction Module"
__description__ = "Self-contained density prediction module for MWLT Transformer"
__url__ = "https://github.com/mwlt/initial_density"
__license__ = "MIT"
__copyright__ = "Copyright 2024 MWLT Team"

def get_version():
    """Return the version string."""
    return __version__

def get_info():
    """Return module information."""
    return {
        'title': __title__,
        'version': __version__,
        'description': __description__,
        'author': __author__,
        'email': __email__,
        'url': __url__,
        'license': __license__,
        'copyright': __copyright__
    }

def list_models():
    """List available model architectures."""
    return ['MWLT_Small', 'MWLT_Base', 'MWLT_Large']

def list_supported_curves():
    """List supported input and output curves."""
    return {
        'input_curves': ['GR', 'AC', 'CNL', 'RLLD'],
        'output_curves': ['DENSITY', 'DEN', 'RHOB'],
        'alternative_names': {
            'GR': ['GAMMA_RAY', 'GR_API'],
            'AC': ['ACOUSTIC', 'DT', 'TRANSIT_TIME'],
            'CNL': ['NEUTRON', 'NPHI', 'CNL_POR'],
            'RLLD': ['RESISTIVITY', 'RES', 'DEEP_RES'],
            'DENSITY': ['DEN', 'RHOB', 'BULK_DENSITY']
        }
    }

# Print module info when imported
print(f"Initial Density Prediction Module v{__version__} loaded successfully")
print(f"Available models: {', '.join(list_models())}")
