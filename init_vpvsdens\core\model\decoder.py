"""
Decoder module for General Well Log Transformer - import wrapper

This module imports the unchanged GeneralDecoder from the original
vp_predictor codebase to avoid duplication while maintaining the specialized
directory structure for vpvsdens.
"""

# Import the unchanged decoder from original codebase
from ...init_vp_pred.vp_predictor.core.decoder import GeneralDecoder

# Re-export for convenience
__all__ = ['GeneralDecoder']
