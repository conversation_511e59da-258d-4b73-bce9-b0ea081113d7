"""
General Well Log Dataset for single-target flexible training

This module provides a flexible dataset implementation that can handle any single 
curve as target while supporting configurable input curves and robust data handling.
"""

import torch
import numpy as np
from typing import Dict, List, Optional, Union, Tuple, Any
import logging
import warnings

from ..configs.curves import CURVE_CONFIGURATIONS
from .normalizer import GeneralDataNormalizer

logger = logging.getLogger(__name__)


class GeneralWellLogDataset(torch.utils.data.Dataset):
    """
    Single-target flexible dataset supporting any curve as output with configurable inputs
    
    This dataset generalizes the VpDataset to support any single target curve while 
    maintaining backward compatibility and adding robust data handling features.
    """
    
    def __init__(
        self,
        data_dict: Dict[str, np.ndarray],
        input_curves: List[str],
        target_curve: str,
        normalizer: Optional[GeneralDataNormalizer] = None,
        sequence_config: Optional[Dict[str, Any]] = None,
        missing_data_strategy: str = 'interpolation',
        quality_threshold: float = 0.8,
        transform: bool = False,
        device: Optional[torch.device] = None
    ):
        """
        Initialize GeneralWellLogDataset
        
        Args:
            data_dict: Dictionary containing curve data {curve_name: np.array}
            input_curves: List of input curve names (e.g., ['GR', 'CNL', 'DEN', 'RLLD'])
            target_curve: Single target curve name (e.g., 'AC', 'DEN', 'CNL')
            normalizer: GeneralDataNormalizer instance for data preprocessing
            sequence_config: Configuration for sequence handling
            missing_data_strategy: Strategy for handling missing data ('interpolation', 'masking', 'skip')
            quality_threshold: Minimum data quality threshold (0.0-1.0)
            transform: Whether to apply data augmentation
            device: Target device for tensors
        """
        self.data_dict = data_dict
        self.input_curves = input_curves
        self.target_curve = target_curve
        self.normalizer = normalizer
        self.missing_data_strategy = missing_data_strategy
        self.quality_threshold = quality_threshold
        self.transform = transform
        self.device = device or torch.device('cpu')
        
        # Default sequence configuration
        default_sequence_config = {
            'total_length': 720,
            'effective_length': 640,
            'augmentation': True,
            'missing_data_handling': missing_data_strategy,
            # Optional stride for windowing; if None, defaults to 50% overlap
            'step_size': None
        }
        self.sequence_config = {**default_sequence_config, **(sequence_config or {})}
        
        self.total_seqlen = self.sequence_config['total_length']
        self.effect_seqlen = self.sequence_config['effective_length']
        # Default to 50% overlap if not provided
        self.step_size = int(self.sequence_config.get('step_size') or max(1, self.total_seqlen // 2))
        
        # Validate inputs
        self._validate_configuration()
        
        # Prepare data
        self._prepare_data()
        
        logger.info(f"GeneralWellLogDataset initialized:")
        logger.info(f"  Input curves: {input_curves}")
        logger.info(f"  Target curve: {target_curve}")
        logger.info(f"  Samples: {len(self)}")
        logger.info(f"  Sequence: {self.total_seqlen} -> {self.effect_seqlen} (step={self.step_size})")
    
    def _validate_configuration(self) -> None:
        """Validate dataset configuration"""
        # Check if target curve is valid
        if self.target_curve not in CURVE_CONFIGURATIONS:
            raise ValueError(f"Target curve '{self.target_curve}' not found in CURVE_CONFIGURATIONS")
        
        # Check if all input curves are valid
        for curve in self.input_curves:
            if curve not in CURVE_CONFIGURATIONS:
                raise ValueError(f"Input curve '{curve}' not found in CURVE_CONFIGURATIONS")
        
        # Check if target curve is in data
        if self.target_curve not in self.data_dict:
            raise ValueError(f"Target curve '{self.target_curve}' not found in data_dict")
        
        # Check if all input curves are in data
        for curve in self.input_curves:
            if curve not in self.data_dict:
                raise ValueError(f"Input curve '{curve}' not found in data_dict")
        
        # Validate sequence lengths
        if self.effect_seqlen > self.total_seqlen:
            raise ValueError(f"Effective length ({self.effect_seqlen}) cannot be greater than total length ({self.total_seqlen})")
        # Validate step size
        if self.step_size < 1 or self.step_size > self.total_seqlen:
            raise ValueError(f"Invalid step_size ({self.step_size}); must be in [1, {self.total_seqlen}]")
    
    def _prepare_data(self) -> None:
        """Prepare and validate data arrays"""
        # Get data lengths
        data_lengths = {curve: len(data) for curve, data in self.data_dict.items()}
        
        # Find common length
        lengths = [data_lengths[curve] for curve in [self.target_curve] + self.input_curves]
        if len(set(lengths)) > 1:
            min_length = min(lengths)
            logger.warning(f"Data curves have different lengths: {data_lengths}")
            logger.warning(f"Truncating all curves to minimum length: {min_length}")
        else:
            min_length = lengths[0]
        
        # Calculate number of samples with configured step_size
        if min_length < self.total_seqlen:
            raise ValueError(f"Data length ({min_length}) is less than required sequence length ({self.total_seqlen})")
        available = min_length - self.total_seqlen
        self.num_samples = 1 + (available // self.step_size)

        # Log estimated memory footprint (float32)
        num_inputs = self.num_samples * len(self.input_curves) * self.total_seqlen
        num_targets = self.num_samples * self.total_seqlen
        total_bytes = (num_inputs + num_targets) * 4  # float32
        mb = total_bytes / (1024 ** 2)
        logger.info(
            f"Preparing dataset windows: min_length={min_length}, total_seqlen={self.total_seqlen}, "
            f"step_size={self.step_size}, num_samples={self.num_samples}, est_mem={mb:.2f} MB"
        )

        # Prepare input data array: [num_samples, num_input_curves, total_seqlen] (float32)
        self.input_data = np.zeros((self.num_samples, len(self.input_curves), self.total_seqlen), dtype=np.float32)
        
        for i, curve in enumerate(self.input_curves):
            curve_data = self.data_dict[curve][:min_length]
            for sample_idx in range(self.num_samples):
                start = sample_idx * self.step_size
                end = start + self.total_seqlen
                self.input_data[sample_idx, i, :] = curve_data[start:end]
                # Progress log at ~10% intervals (log once per sample index using first curve)
                if i == 0 and self.num_samples >= 10:
                    log_every = max(1, self.num_samples // 10)
                    if sample_idx % log_every == 0:
                        pct = (sample_idx / self.num_samples) * 100
                        logger.info(f"Windowing progress: {pct:.0f}% ({sample_idx}/{self.num_samples})")
        
        # Prepare target data array: [num_samples, total_seqlen]
        target_data = self.data_dict[self.target_curve][:min_length]
        self.target_data = np.zeros((self.num_samples, self.total_seqlen), dtype=np.float32)
        
        for sample_idx in range(self.num_samples):
            start = sample_idx * self.step_size
            end = start + self.total_seqlen
            self.target_data[sample_idx, :] = target_data[start:end]
            if self.num_samples >= 10:
                log_every = max(1, self.num_samples // 10)
                if sample_idx % log_every == 0:
                    pct = (sample_idx / self.num_samples) * 100
                    logger.info(f"Target windowing progress: {pct:.0f}% ({sample_idx}/{self.num_samples})")
        
        # Apply normalization if available
        if self.normalizer is not None:
            self._apply_normalization()
        
        # Calculate data quality statistics
        self._calculate_quality_stats()
    
    def _apply_normalization(self) -> None:
        """Apply data normalization using the normalizer"""
        try:
            # Normalize input curves
            for i, curve in enumerate(self.input_curves):
                for sample_idx in range(self.num_samples):
                    self.input_data[sample_idx, i, :] = self.normalizer.normalize_curve(
                        self.input_data[sample_idx, i, :], curve
                    )
                    # Progress log ~10% per curve
                    if self.num_samples >= 10:
                        log_every = max(1, self.num_samples // 10)
                        if sample_idx % log_every == 0:
                            pct = (sample_idx / self.num_samples) * 100
                            logger.info(f"Normalization (inputs:{curve}) progress: {pct:.0f}%")
            
            # Normalize target curve
            for sample_idx in range(self.num_samples):
                self.target_data[sample_idx, :] = self.normalizer.normalize_curve(
                    self.target_data[sample_idx, :], self.target_curve
                )
                if self.num_samples >= 10:
                    log_every = max(1, self.num_samples // 10)
                    if sample_idx % log_every == 0:
                        pct = (sample_idx / self.num_samples) * 100
                        logger.info(f"Normalization (target:{self.target_curve}) progress: {pct:.0f}%")
                
        except Exception as e:
            logger.warning(f"Normalization failed: {e}. Proceeding without normalization.")
    
    def _calculate_quality_stats(self) -> None:
        """Calculate data quality statistics"""
        # Calculate percentage of valid (non-NaN) data
        input_valid = np.isfinite(self.input_data).mean()
        target_valid = np.isfinite(self.target_data).mean()
        
        self.quality_stats = {
            'input_valid_ratio': input_valid,
            'target_valid_ratio': target_valid,
            'overall_valid_ratio': min(input_valid, target_valid)
        }
        
        logger.info(f"Data quality: {self.quality_stats['overall_valid_ratio']:.2%} valid data")
        
        if self.quality_stats['overall_valid_ratio'] < self.quality_threshold:
            logger.warning(f"Data quality ({self.quality_stats['overall_valid_ratio']:.2%}) below threshold ({self.quality_threshold:.2%})")
    
    def __len__(self) -> int:
        """Return number of samples"""
        return self.num_samples
    
    def __getitem__(self, idx: int) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Get a single sample
        
        Args:
            idx: Sample index
            
        Returns:
            Tuple of (input_tensor, target_tensor)
            - input_tensor: [num_input_curves, effect_seqlen] 
            - target_tensor: [1, effect_seqlen] (single target curve)
        """
        if idx >= len(self):
            raise IndexError(f"Index {idx} out of range for dataset of size {len(self)}")
        
        # Get input curves and target
        inputs = self.input_data[idx]  # Shape: [num_input_curves, total_seqlen]
        target = self.target_data[idx]  # Shape: [total_seqlen]
        
        # Apply data augmentation if requested
        if self.transform and self.total_seqlen > self.effect_seqlen:
            start_idx = np.random.randint(0, self.total_seqlen - self.effect_seqlen + 1)
            inputs = inputs[:, start_idx:start_idx + self.effect_seqlen]
            target = target[start_idx:start_idx + self.effect_seqlen]
        else:
            inputs = inputs[:, :self.effect_seqlen]
            target = target[:self.effect_seqlen]
        
        # Handle missing data
        inputs, target = self._handle_missing_data(inputs, target)
        
        # CRITICAL FIX: Apply normalization to target values if normalizer is available
        # This ensures consistency with training expectations
        if self.normalizer is not None:
            target_normalized = self.normalizer.normalize_curve(target, self.target_curve)
            target_tensor = torch.FloatTensor(target_normalized).unsqueeze(0).to(self.device)  # [1, effect_seqlen]
        else:
            target_tensor = torch.FloatTensor(target).unsqueeze(0).to(self.device)  # [1, effect_seqlen]
        
        # Convert inputs to tensors
        input_tensor = torch.FloatTensor(inputs).to(self.device)
        
        return input_tensor, target_tensor
    
    def _handle_missing_data(
        self, 
        inputs: np.ndarray, 
        target: np.ndarray
    ) -> Tuple[np.ndarray, np.ndarray]:
        """Handle missing data according to configured strategy"""
        if self.missing_data_strategy == 'interpolation':
            # Simple linear interpolation for NaN values
            for i in range(inputs.shape[0]):
                mask = np.isnan(inputs[i])
                if mask.any():
                    inputs[i] = self._interpolate_nan(inputs[i])
            
            if np.isnan(target).any():
                target = self._interpolate_nan(target)
                
        elif self.missing_data_strategy == 'masking':
            # Replace NaN with zeros (will be handled by model)
            inputs = np.nan_to_num(inputs, nan=0.0)
            target = np.nan_to_num(target, nan=0.0)
            
        elif self.missing_data_strategy == 'skip':
            # This would require dataset-level filtering, implemented in __getitem__ logic
            # For now, convert to zero
            inputs = np.nan_to_num(inputs, nan=0.0)
            target = np.nan_to_num(target, nan=0.0)
        
        return inputs, target
    
    def _interpolate_nan(self, data: np.ndarray) -> np.ndarray:
        """Simple linear interpolation for NaN values"""
        mask = np.isnan(data)
        if not mask.any():
            return data
        
        data_interp = data.copy()
        valid_indices = np.where(~mask)[0]
        
        if len(valid_indices) == 0:
            # All values are NaN, replace with zeros
            return np.zeros_like(data)
        
        # Interpolate NaN values
        for i in np.where(mask)[0]:
            if i < valid_indices[0]:
                # Extrapolate forward
                data_interp[i] = data[valid_indices[0]]
            elif i > valid_indices[-1]:
                # Extrapolate backward
                data_interp[i] = data[valid_indices[-1]]
            else:
                # Interpolate between valid points
                left_idx = valid_indices[valid_indices < i][-1]
                right_idx = valid_indices[valid_indices > i][0]
                
                # Linear interpolation
                weight = (i - left_idx) / (right_idx - left_idx)
                data_interp[i] = data[left_idx] * (1 - weight) + data[right_idx] * weight
        
        return data_interp
    
    def get_curve_info(self) -> Dict[str, Any]:
        """Get information about curves in the dataset"""
        return {
            'input_curves': self.input_curves,
            'target_curve': self.target_curve,
            'num_input_curves': len(self.input_curves),
            'sequence_config': self.sequence_config,
            'quality_stats': self.quality_stats
        }
    
    def get_sample_stats(self) -> Dict[str, Any]:
        """Get dataset statistics"""
        return {
            'num_samples': len(self),
            'total_seqlen': self.total_seqlen,
            'effect_seqlen': self.effect_seqlen,
            'data_quality': self.quality_stats,
            'missing_data_strategy': self.missing_data_strategy
        }


class VpDatasetCompatible(GeneralWellLogDataset):
    """
    Backward-compatible wrapper for VpDataset using GeneralWellLogDataset
    
    This provides exact API compatibility with the original VpDataset while 
    using the new flexible implementation underneath.
    """
    
    def __init__(
        self,
        input_data: np.ndarray,
        target_data: np.ndarray,
        normalizer: Optional[Any] = None,
        total_seqlen: int = 720,
        effect_seqlen: int = 640,
        transform: bool = False
    ):
        """
        Initialize VpDataset-compatible wrapper
        
        Args:
            input_data: Input curve data [N, 4, seqlen] for [GR, CNL, DEN, RLLD]
            target_data: Target AC data [N, seqlen] 
            normalizer: Data normalizer (VpDataNormalizer compatible)
            total_seqlen: Total sequence length
            effect_seqlen: Effective sequence length
            transform: Whether to apply augmentation
        """
        # Convert array data to dictionary format
        num_samples = input_data.shape[0]
        curve_names = ['GR', 'CNL', 'DEN', 'RLLD']
        
        data_dict = {}
        
        # Flatten input curves: [N, 4, seqlen] -> [N*seqlen] per curve
        for i, curve in enumerate(curve_names):
            curve_data = input_data[:, i, :].flatten()  # [N*seqlen]
            data_dict[curve] = curve_data
        
        # Flatten target data: [N, seqlen] -> [N*seqlen]
        data_dict['AC'] = target_data.flatten()
        
        # Convert normalizer if needed
        general_normalizer = None
        if normalizer is not None:
            # Wrap the VpDataNormalizer to be compatible with GeneralDataNormalizer
            general_normalizer = self._wrap_vp_normalizer(normalizer)
        
        # Sequence configuration
        sequence_config = {
            'total_length': total_seqlen,
            'effective_length': effect_seqlen,
            'augmentation': transform
        }
        
        # Initialize parent
        super().__init__(
            data_dict=data_dict,
            input_curves=curve_names,
            target_curve='AC',
            normalizer=general_normalizer,
            sequence_config=sequence_config,
            transform=transform
        )
    
    def _wrap_vp_normalizer(self, vp_normalizer):
        """Wrap VpDataNormalizer to be compatible with GeneralDataNormalizer"""
        # This is a simple wrapper - in practice, you might need to implement
        # a more sophisticated adapter depending on the VpDataNormalizer interface
        class VpNormalizerWrapper:
            def __init__(self, vp_norm):
                self.vp_norm = vp_norm
                
            def normalize_curve(self, data, curve_name):
                if curve_name == 'AC':
                    # Use VP normalization method
                    return self.vp_norm.normalize_vp(data)
                else:
                    # Use input normalization method
                    return self.vp_norm.normalize_inputs(data.reshape(1, -1))[0]
        
        return VpNormalizerWrapper(vp_normalizer)


# Convenience function for creating datasets
def create_dataset_from_template(
    data_dict: Dict[str, np.ndarray],
    template_name: str,
    normalizer: Optional[GeneralDataNormalizer] = None,
    **kwargs
) -> GeneralWellLogDataset:
    """
    Create dataset from a model template
    
    Args:
        data_dict: Dictionary of curve data
        template_name: Model template name (from MODEL_TEMPLATES)
        normalizer: Data normalizer
        **kwargs: Additional dataset parameters
        
    Returns:
        GeneralWellLogDataset instance
    """
    from ..configs.models import MODEL_TEMPLATES
    
    if template_name not in MODEL_TEMPLATES:
        raise ValueError(f"Template '{template_name}' not found in MODEL_TEMPLATES")
    
    template = MODEL_TEMPLATES[template_name]
    
    # Extract single target curve (Phase 2 focuses on single-target)
    output_curves = template['output_curves']
    if len(output_curves) != 1:
        raise ValueError(f"Template '{template_name}' must have exactly one output curve for Phase 2")
    
    return GeneralWellLogDataset(
        data_dict=data_dict,
        input_curves=template['input_curves'],
        target_curve=output_curves[0],
        normalizer=normalizer,
        **kwargs
    )


def create_general_dataset(
    data_dict: Dict[str, np.ndarray],
    input_curves: List[str], 
    target_curve: str,
    normalizer: Optional[Any] = None,
    sequence_config: Optional[Dict] = None,
    missing_data_strategy: str = 'interpolation',
    quality_threshold: float = 0.8,
    transform: bool = False,
    device: Optional[torch.device] = None
) -> GeneralWellLogDataset:
    """
    Convenience function to create a GeneralWellLogDataset instance
    
    Args:
        data_dict: Dictionary of curve data {curve_name: np.ndarray}
        input_curves: List of input curve names
        target_curve: Target curve name
        normalizer: Data normalizer instance
        sequence_config: Configuration for sequence length
        missing_data_strategy: How to handle missing data
        quality_threshold: Data quality threshold
        transform: Whether to apply data augmentation
        device: PyTorch device
    
    Returns:
        GeneralWellLogDataset instance
    """
    # Set defaults
    if sequence_config is None:
        sequence_config = {'effect_seqlen': 640, 'total_seqlen': 640}
    
    if device is None:
        device = torch.device('cpu')
    
    # Convert normalizer if needed
    general_normalizer = None
    if normalizer is not None:
        from .normalizer import GeneralDataNormalizer
        if isinstance(normalizer, GeneralDataNormalizer):
            general_normalizer = normalizer
        else:
            # Wrap VpDataNormalizer to be compatible
            class CompatibleNormalizer:
                def __init__(self, vp_normalizer):
                    self.vp_normalizer = vp_normalizer
                
                def normalize_curve(self, data: np.ndarray, curve_name: str) -> np.ndarray:
                    """Normalize curve data"""
                    if curve_name == 'AC':
                        # Use VP normalizer for AC curve
                        return self.vp_normalizer.normalize_vp(torch.FloatTensor(data)).numpy()
                    else:
                        # Use standard normalization for other curves
                        return (data - data.mean()) / (data.std() + 1e-8)
            
            general_normalizer = CompatibleNormalizer(normalizer)
    
    return GeneralWellLogDataset(
        data_dict=data_dict,
        input_curves=input_curves,
        target_curve=target_curve,
        normalizer=general_normalizer,
        sequence_config=sequence_config,
        missing_data_strategy=missing_data_strategy,
        quality_threshold=quality_threshold,
        transform=transform,
        device=device
    )