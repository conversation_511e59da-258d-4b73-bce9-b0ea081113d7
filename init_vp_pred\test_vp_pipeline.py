#!/usr/bin/env python3
"""
Test script for the refactored VP prediction pipeline
Verifies that all components work correctly and integration is successful

Usage:
    python test_vp_pipeline.py
"""
import sys
import time
from pathlib import Path

# Add current directory to path for imports (pragmatic for local test execution)
sys.path.insert(0, str(Path(__file__).parent))

def test_imports():
    """
    Test that all required imports work correctly.

    Verifies that core pipeline classes can be imported from train_vp_improved module
    and checks vp_predictor package integration status.
    """
    print("[TEST] Testing imports...")

    try:
        from train_vp_improved import (
            VpPipelineConfig, VpDataProcessor, VpTrainingManager,
            VpValidationManager, VpPredictionManager, VpPredictionPipeline,
            VpDataset
        )
        print("   [OK] Core pipeline classes imported successfully")
    except ImportError as e:
        print(f"   [ERROR] Core imports failed: {e}")
        return False

    try:
        # Test vp_predictor integration
        from train_vp_improved import USING_VP_PREDICTOR_PACKAGE
        print(f"   [INFO] Using vp_predictor package: {USING_VP_PREDICTOR_PACKAGE}")
    except ImportError as e:
        print(f"   [WARN] vp_predictor integration check failed: {e}")

    return True

def test_configuration():
    """
    Test configuration management.

    Verifies that VpPipelineConfig can load default settings and handle custom overrides
    correctly, ensuring proper configuration hierarchy and value retrieval.
    """
    print("\n[TEST] Testing configuration management...")

    try:
        from train_vp_improved import VpPipelineConfig

        # Test default configuration
        config = VpPipelineConfig()
        assert config.get('model.type') == 'base'
        assert config.get('training.batch_size') == 8
        assert config.get('validation.cv_folds') == 5
        print("   [OK] Default configuration loaded correctly")

        # Test configuration override
        custom_config = {
            'model': {'type': 'large'},
            'training': {'batch_size': 16}
        }
        config = VpPipelineConfig(custom_config)
        assert config.get('model.type') == 'large'
        assert config.get('training.batch_size') == 16
        assert config.get('validation.cv_folds') == 5  # Should keep default
        print("   [OK] Configuration override works correctly")

        return True

    except Exception as e:
        print(f"   [ERROR] Configuration test failed: {e}")
        return False

def test_data_processor():
    """
    Test data processor functionality.

    Verifies that VpDataProcessor can initialize correctly and detect data files,
    ensuring the data processing pipeline components are functional.
    """
    print("\n[TEST] Testing data processor...")

    try:
        from train_vp_improved import VpDataProcessor, VpPipelineConfig

        config = VpPipelineConfig()
        processor = VpDataProcessor(config)

        # Test data file detection
        data_files = processor.find_data_files()
        if data_files:
            print(f"   [OK] Data files found: {len(data_files)} files")
            for file_path in data_files:
                print(f"      - {file_path}")
        else:
            print("   [WARN] No data files found (A1.hdf5, A2.hdf5)")
            print("      This is expected if data files are not available")

        print("   [OK] Data processor initialized successfully")
        return True

    except Exception as e:
        print(f"   [ERROR] Data processor test failed: {e}")
        return False

def test_pipeline_initialization():
    """
    Test pipeline initialization.

    Verifies that VpPredictionPipeline can be initialized with default and custom settings,
    and that directory structure creation works correctly.
    """
    print("\n[TEST] Testing pipeline initialization...")

    try:
        from train_vp_improved import VpPredictionPipeline

        # Test default initialization
        pipeline = VpPredictionPipeline()
        assert pipeline.config is not None
        assert pipeline.output_dir is not None
        print("   [OK] Pipeline initialized with defaults")

        # Test custom output directory
        custom_output = "test_vp_outputs"
        pipeline = VpPredictionPipeline(output_dir=custom_output)
        assert str(pipeline.output_dir) == custom_output
        print("   [OK] Pipeline initialized with custom output directory")

        # Test directory setup
        pipeline.setup_directories()
        expected_dirs = ['training', 'validation', 'prediction']
        for dir_name in expected_dirs:
            dir_path = pipeline.output_dir / dir_name
            assert dir_path.exists(), f"Directory {dir_path} was not created"
        print("   [OK] Directory structure created successfully")

        # Cleanup test directory
        import shutil
        if Path(custom_output).exists():
            shutil.rmtree(custom_output)

        return True

    except Exception as e:
        print(f"   [ERROR] Pipeline initialization test failed: {e}")
        return False

def test_model_creation():
    """
    Test model creation functionality.

    Verifies that VpTrainingManager can create models, setup training components,
    and handle temporary directory cleanup properly.
    """
    print("\n[TEST] Testing model creation...")

    try:
        from train_vp_improved import VpTrainingManager, VpPipelineConfig
        import tempfile
        import shutil

        config = VpPipelineConfig()

        # Use a shorter, more reliable temporary directory path
        temp_base = Path.cwd() / "temp_test_model"
        if temp_base.exists():
            shutil.rmtree(temp_base)

        try:
            temp_base.mkdir(exist_ok=True)
            output_dir = temp_base
            training_manager = VpTrainingManager(config, output_dir)

            # Test model creation
            training_manager.create_model()
            assert training_manager.model is not None
            print("   [OK] Model created successfully")

            # Test training setup
            training_manager.setup_training()
            assert training_manager.optimizer is not None
            assert training_manager.criterion is not None
            assert training_manager.early_stopping is not None
            print("   [OK] Training components setup successfully")

            # Close logger handlers to release file locks
            if hasattr(training_manager, 'logger') and training_manager.logger:
                for handler in training_manager.logger.handlers[:]:
                    handler.close()
                    training_manager.logger.removeHandler(handler)

        finally:
            # Clean up
            if temp_base.exists():
                try:
                    shutil.rmtree(temp_base)
                except PermissionError:
                    # If still locked, try again after a short delay
                    time.sleep(0.1)
                    try:
                        shutil.rmtree(temp_base)
                    except PermissionError:
                        print("   [WARN] Warning: Could not clean up temporary directory (file still in use)")
                        pass

        return True

    except Exception as e:
        print(f"   [ERROR] Model creation test failed: {e}")
        return False

def test_command_line_interface():
    """
    Test command line interface.

    Verifies that the train_vp_improved.py script can display help information
    and that required command-line arguments are properly documented.
    """
    print("\n[TEST] Testing command line interface...")

    try:
        import subprocess
        import sys

        # Test help command
        result = subprocess.run([
            sys.executable, 'train_vp_improved.py', '--help'
        ], capture_output=True, text=True, cwd=Path(__file__).parent)

        if result.returncode == 0:
            print("   [OK] Command line help works correctly")
            # Check if key arguments are present
            help_text = result.stdout
            required_args = ['--config', '--output-dir', '--stage', '--model-path']
            for arg in required_args:
                if arg in help_text:
                    print(f"      - {arg} argument available")
                else:
                    print(f"      [WARN] {arg} argument not found in help")
        else:
            print(f"   [ERROR] Command line help failed: {result.stderr}")
            return False

        return True

    except Exception as e:
        print(f"   [ERROR] Command line interface test failed: {e}")
        return False

def run_comprehensive_test():
    """
    Run comprehensive test suite.

    Executes all individual test functions and reports overall results,
    providing guidance for next steps based on test outcomes.
    """
    print("[START] Starting VP Prediction Pipeline Test Suite")
    print("=" * 60)

    tests = [
        ("Import Test", test_imports),
        ("Configuration Test", test_configuration),
        ("Data Processor Test", test_data_processor),
        ("Pipeline Initialization Test", test_pipeline_initialization),
        ("Model Creation Test", test_model_creation),
        ("Command Line Interface Test", test_command_line_interface)
    ]

    passed_tests = 0
    total_tests = len(tests)

    for test_name, test_func in tests:
        try:
            if test_func():
                passed_tests += 1
        except Exception as e:
            print(f"   [ERROR] {test_name} failed with exception: {e}")

    print("\n" + "=" * 60)
    print("[RESULTS] Test Suite Results")
    print("=" * 60)
    print(f"Tests passed: {passed_tests}/{total_tests}")

    if passed_tests == total_tests:
        print("[SUCCESS] All tests passed! The VP prediction pipeline is ready to use.")
        print("\n[INFO] Next Steps:")
        print("1. Ensure A1.hdf5 and A2.hdf5 data files are available")
        print("2. Run the complete pipeline: python train_vp_improved.py")
        print("3. Check results in vp_prediction_outputs/ directory")
        return True
    else:
        print(f"[WARN] {total_tests - passed_tests} test(s) failed. Please review the issues above.")
        print("\n[TROUBLESHOOT] Troubleshooting:")
        print("1. Check that all dependencies are installed")
        print("2. Verify vp_predictor package is available or local modules exist")
        print("3. Ensure Python environment is properly configured")
        return False

if __name__ == "__main__":
    success = run_comprehensive_test()
    sys.exit(0 if success else 1)
