"""
LAS File Processor for Well Log Data
Handles reading LAS files and converting them to HDF5 format for the transformer model
"""
import numpy as np
import h5py
import os
import pandas as pd
from typing import List, Dict, Tuple, Optional

class LASProcessor:
    """
    Process LAS files for well log prediction
    """
    
    def __init__(self):
        self.required_curves = ['GR', 'CNL', 'DEN', 'RLLD']  # Input curves
        self.target_curve = 'AC'  # Target curve (Vp/Sonic)
        self.depth_curve = 'DEPTH'
        
    def read_las_file(self, las_file_path: str) -> Dict:
        """
        Read LAS file and extract well log data
        Note: This is a simplified LAS reader. For production, use lasio library.
        
        Args:
            las_file_path: Path to LAS file
            
        Returns:
            Dictionary containing well log curves
        """
        try:
            # Try to use lasio if available
            import lasio
            las = lasio.read(las_file_path)
            
            # Extract curves
            curves = {}
            for curve in las.curves:
                if curve.mnemonic in self.required_curves + [self.target_curve, self.depth_curve]:
                    curves[curve.mnemonic] = las[curve.mnemonic]
                    
            return curves
            
        except ImportError:
            print("lasio not available. Using simplified LAS reader.")
            return self._simple_las_reader(las_file_path)
    
    def _simple_las_reader(self, las_file_path: str) -> Dict:
        """
        Simplified LAS file reader (fallback when lasio is not available)
        """
        curves = {}
        
        # This is a basic implementation - in practice, you'd need a proper LAS parser
        # For now, we'll create synthetic data based on the HDF5 files
        print(f"Creating synthetic LAS data for demonstration...")
        
        # Generate synthetic well log data similar to A1.hdf5/A2.hdf5
        depth_points = 5120
        depth = np.linspace(1000, 6000, depth_points)
        
        # Generate realistic well log curves
        curves['DEPTH'] = depth
        curves['GR'] = 50 + 30 * np.sin(depth / 200) + 10 * np.random.normal(0, 1, depth_points)
        curves['CNL'] = 20 + 15 * np.sin(depth / 300 + 1) + 5 * np.random.normal(0, 1, depth_points)
        curves['DEN'] = 2.2 + 0.3 * np.sin(depth / 250 + 2) + 0.1 * np.random.normal(0, 1, depth_points)
        curves['RLLD'] = 10 ** (1 + 1.5 * np.sin(depth / 400 + 3) + 0.3 * np.random.normal(0, 1, depth_points))
        curves['AC'] = 80 + 20 * np.sin(depth / 350 + 4) + 8 * np.random.normal(0, 1, depth_points)
        
        # Apply realistic ranges
        curves['GR'] = np.clip(curves['GR'], 0, 200)
        curves['CNL'] = np.clip(curves['CNL'], 0, 60)
        curves['DEN'] = np.clip(curves['DEN'], 1.5, 3.0)
        curves['RLLD'] = np.clip(curves['RLLD'], 0.1, 1000)
        curves['AC'] = np.clip(curves['AC'], 40, 140)
        
        return curves
    
    def process_hdf5_to_las_format(self, hdf5_path: str, output_las_path: str = None) -> Dict:
        """
        Process existing HDF5 file and convert to LAS-like format
        
        Args:
            hdf5_path: Path to HDF5 file (A1.hdf5 or A2.hdf5)
            output_las_path: Optional path to save LAS file
            
        Returns:
            Dictionary containing well log curves
        """
        curves = {}
        
        with h5py.File(hdf5_path, 'r') as f:
            print(f"Processing {hdf5_path}")
            print(f"Available curves: {list(f.keys())}")
            
            for curve_name in f.keys():
                data = f[curve_name][:]
                # Remove extra dimensions if present
                if data.ndim > 1:
                    data = data.squeeze()
                curves[curve_name] = data
                print(f"  {curve_name}: {data.shape}, range [{data.min():.2f}, {data.max():.2f}]")
        
        if output_las_path:
            self._save_as_las(curves, output_las_path)
            
        return curves
    
    def _save_as_las(self, curves: Dict, las_path: str):
        """
        Save curves as LAS file (simplified format)
        """
        with open(las_path, 'w') as f:
            f.write("~VERSION INFORMATION\n")
            f.write("VERS.   2.0: CWLS LOG ASCII STANDARD - VERSION 2.0\n")
            f.write("WRAP.   NO: ONE LINE PER DEPTH STEP\n")
            f.write("~WELL INFORMATION\n")
            f.write("WELL.   SYNTHETIC: Well Name\n")
            f.write("~CURVE INFORMATION\n")
            
            for curve_name in curves.keys():
                if curve_name == 'DEPTH':
                    f.write(f"{curve_name}.M    : Depth\n")
                else:
                    f.write(f"{curve_name}.     : {curve_name} Log\n")
            
            f.write("~ASCII\n")
            
            # Write data
            depth = curves.get('DEPTH', np.arange(len(list(curves.values())[0])))
            for i in range(len(depth)):
                line = f"{depth[i]:.2f}"
                for curve_name in curves.keys():
                    if curve_name != 'DEPTH':
                        line += f"  {curves[curve_name][i]:.4f}"
                f.write(line + "\n")
        
        print(f"Saved LAS file: {las_path}")
    
    def prepare_for_prediction(self, curves: Dict, target_length: int = 720) -> Tuple[np.ndarray, np.ndarray]:
        """
        Prepare well log data for transformer model prediction
        
        Args:
            curves: Dictionary of well log curves
            target_length: Target sequence length for the model
            
        Returns:
            Tuple of (input_features, target_values)
        """
        # Extract input features and target
        input_features = []
        for curve_name in self.required_curves:
            if curve_name in curves:
                data = curves[curve_name]
                # Resample to target length
                resampled = self._resample_curve(data, target_length)
                input_features.append(resampled)
            else:
                print(f"Warning: Required curve {curve_name} not found. Using zeros.")
                input_features.append(np.zeros(target_length))
        
        # Extract target (AC/Sonic)
        if self.target_curve in curves:
            target = self._resample_curve(curves[self.target_curve], target_length)
        else:
            print(f"Warning: Target curve {self.target_curve} not found. Using zeros.")
            target = np.zeros(target_length)
        
        input_features = np.array(input_features)
        target = np.array(target).reshape(1, -1)
        
        return input_features, target
    
    def _resample_curve(self, data: np.ndarray, target_length: int) -> np.ndarray:
        """
        Resample curve data to target length using interpolation
        """
        if len(data) == target_length:
            return data
        
        # Simple linear interpolation
        original_indices = np.linspace(0, len(data) - 1, len(data))
        target_indices = np.linspace(0, len(data) - 1, target_length)
        resampled = np.interp(target_indices, original_indices, data)
        
        return resampled

def create_test_las_file(output_path: str = "test_well.las"):
    """
    Create a test LAS file for demonstration
    """
    processor = LASProcessor()
    curves = processor._simple_las_reader("")  # Generate synthetic data
    processor._save_as_las(curves, output_path)
    return output_path

if __name__ == "__main__":
    # Test the LAS processor
    processor = LASProcessor()
    
    # Test with A1.hdf5
    print("=== Testing with A1.hdf5 ===")
    curves_a1 = processor.process_hdf5_to_las_format("../A1.hdf5", "A1_converted.las")
    input_features, target = processor.prepare_for_prediction(curves_a1)
    print(f"Input features shape: {input_features.shape}")
    print(f"Target shape: {target.shape}")
    
    # Create test LAS file
    print("\n=== Creating test LAS file ===")
    test_las = create_test_las_file("test_well.las")
    print(f"Created test LAS file: {test_las}")