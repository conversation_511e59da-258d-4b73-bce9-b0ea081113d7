# Specialized Architecture Plan: Vp (P‑wave), Vs (S‑wave), and Density (Rho) — init_vpvsdens Delivery

Purpose
- Deliver a concrete, non-disruptive plan to specialize the existing MWLT-based code for three key targets: Vp (compressional, sonic AC), Vs (shear, DTS), and density (DEN/RHOB).
- Minimize architectural/code churn: reuse and merge from init_vp_pred\ and init_density_base\; focus changes on configurations, normalization/denormalization, training templates, and light utilities.
- Create a new folder init_vpvsdens/ to host all modified versions. Original repositories (init_vp_pred\, init_density_base\) remain untouched; copies are modified only inside init_vpvsdens/.

Scope and constraints
- Keep MWLT backbone unchanged (Conv1d+ResCNN → Transformer → Decoder).
- Keep training loop infrastructure from vp_predictor/core/training.py (GeneralTrainingManager) and improved checkpoint IO from init_density_base/core/utils.py.
- Enable single-target training for each: AC (Vp), DTS (Vs), DEN (Rho). Multi-output remains optional/phase-2.
- Avoid large refactors; prefer adding configs and light wrappers.

Domain context (value ranges and units)
- Vp (AC sonic slowness): typical ~40–400 μs/ft depending on lithology and compaction (broad range to cover most cases). [EPA Acoustic Logging, SEG Wiki, Crain’s Handbook]
- Vs (DTS sonic slowness): often larger slowness than AC; typical ~100–1000 μs/ft with strong lithology/compaction dependence; high unconsolidated can exceed this. [EPA Acoustic Logging, SEG Wiki]
- Density (RHOB): ~1.5–3.0 g/cc typical for sedimentary rocks; 2.0–2.9 common in training data. 

References used (via Brave web search)
- EPA Acoustic logging (P and S components): https://www.epa.gov/environmental-geophysics/logging-techniques-and-tools-acoustic-logging
- SEG Wiki: Sonic logs overview: https://wiki.seg.org/wiki/Sonic_logs
- Acoustic logging overview: https://archive.epa.gov/esd/archive-geophysics/web/html/acoustic_logging.html
- (Context: Crain’s Petrophysical Handbook, sonic travel time; general ranges)

New folder: init_vpvsdens/ (all modified files live here)
- init_vpvsdens/
  - core/
    - configs/            # COPIED from init_vp_pred/vp_predictor/configs then modified
      - curves.py         # + DTS/VS, RHOB entries; tuned ranges/normalizations
      - training.py       # + shear_training & integrated standard_shear
      - models.py         # copy for template completeness (unchanged or minimal edits)
      - validation.py     # copy (unchanged)
    - training/
      - losses.py         # COPIED from vp core/loss_functions.py; + optional create_shear_loss
      - manager.py        # COPY of GeneralTrainingManager OR import existing (no change needed)
      - checkpoint.py     # NEW: robust save/load (from init_density_base/core/utils.py)
      - early_stopping.py # COPY (optional), or import legacy
      - metrics.py        # COPY of RMSE/R²/MAE helpers
    - data/
      - dataset.py        # IMPORT existing GeneralWellLogDataset (no change) or copy if needed
      - normalizer.py     # IMPORT existing GeneralDataNormalizer (no change)
    - model/
      - transformer.py    # IMPORT GeneralWellLogTransformer (no change)
      - decoder.py        # IMPORT GeneralDecoder (no change)
  - api/
    - vs_predictor.py     # OPTIONAL thin wrapper for DTS (Vs)
  - README.md             # OPTIONAL: local readme for Vp/Vs/Rho specialization

Plan overview (what changes and where)
1) Curves configuration: add/adjust entries in init_vpvsdens/core/configs/curves.py (copied from init_vp_pred/vp_predictor/configs/curves.py)
   - Add DTS and VS alias; add RHOB alias for DEN; verify AC/VP aliases.
   - Set physics ranges and normalization methods specific to AC, DTS, DEN.
2) Normalization/denormalization: rely on GeneralDataNormalizer (init_vpvsdens/core/data/normalizer.py)
   - Prefer importing unmodified normalizer from init_vp_pred if no changes are required; keep all normalization spec in configs/curves.py.
3) Training templates: extend init_vpvsdens/core/configs/training.py (copied from vp configs)
   - Add shear_training for DTS (Vs) with tuned hyperparameters; reuse vp_training and density_training entries.
4) Loss functions: reuse GeneralWellLogLoss via init_vpvsdens/core/training/losses.py (copied)
   - Add optional create_shear_loss (DTS) using CurveSpecificLossFactory.
5) Checkpoint I/O: create init_vpvsdens/core/training/checkpoint.py
   - Populate with robust save/load logic adapted from init_density_base/core/utils.py; update manager to use it.
6) Light API wrappers (optional)
   - init_vpvsdens/api/vs_predictor.py mirroring VpPredictor; specialized convenience for DTS.

Detailed specification

A) Curves configuration changes (init_vpvsdens/core/configs/curves.py)
- Maintain existing entries for AC (and VP alias) and DEN copied from vp configs.
- Add DTS (shear slowness) and VS alias:
  - Name: Shear Sonic (DTS)
  - Unit: μs/ft
  - Physics range: (100, 1000) [broad, adjustable per basin]
  - Normalization: zscore with mean≈400, std≈200, clip_range (-3, 3)
  - Activation: none (raw output; apply physics clamp in loss/denorm)
- Add RHOB alias for DEN:
  - Type: both; same physics range and normalization as DEN.
- Preserve RLLD (log10 preprocessing) and other input curves as-is.

Example additions (illustrative):
```python path=null start=null
# New Vs entries (aliases)
CURVE_CONFIGURATIONS['DTS'] = {
    'name': 'Shear Sonic Slowness',
    'unit': 'μs/ft',
    'type': 'both',
    'physics_range': (100, 1000),
    'normalization': {
        'method': 'zscore',
        'mean': 400.0,
        'std': 200.0,
        'clip_range': (-3, 3)
    },
    'preprocessing': None,
    'activation': 'none',
    'description': 'Shear slowness (Vs proxy)'
}
CURVE_CONFIGURATIONS['VS'] = {
    **CURVE_CONFIGURATIONS['DTS'],
    'name': 'Shear Velocity (alias DTS)',
    'type': 'output'
}
CURVE_CONFIGURATIONS['RHOB'] = {
    **CURVE_CONFIGURATIONS['DEN'],
    'name': 'Bulk Density (alias)'
}
```

B) Normalization & denormalization behavior
- Use GeneralDataNormalizer so changes stay in configs.
- AC (Vp): zscore mean≈200, std≈75; activation none; clamp to (40,400) after denorm.
- DTS (Vs): zscore mean≈400, std≈200; activation none; clamp to (100,1000) after denorm; refine stats using compute_data_statistics().
- DEN/RHOB: zscore mean≈2.5, std≈0.3; activation either 'none' with physics clamp post-denorm, or 'sigmoid' if keeping normalized decoding.
- RLLD: keep log10 preprocessing as in existing configs.

C) Loss functions (init_vpvsdens/core/training/losses.py)
- AC (Vp): create_vp_loss; physics_constraints=True.
- DTS (Vs): add create_shear_loss using MSE (consider Huber if outliers); physics_constraints=True.
- DEN: keep create_density_loss with constraint_weight≈0.5.
- Optional phase-2: add soft Vp/Vs ratio penalty when jointly training.

D) Training configurations (init_vpvsdens/core/configs/training.py)
- Keep existing vp_training and density_training entries (copied).
- Add shear_training tuned for DTS and integrated template standard_shear.

E) Data pipeline and features
- Inputs: GR, CNL/NPHI, DEN, RLLD, optional CALI/SP.
- Target per run: AC or DTS or DEN.
- Windows: 720→640 with ~50% overlap; light augmentations optional.
- Missing data: use GeneralWellLogDataset strategies.

F) Checkpointing and evaluation
- Use init_vpvsdens/core/training/checkpoint.py for robust torch >=2.6 compatible IO.
- Evaluate RMSE, MAE, R² on denormalized outputs; optionally compute Vp/Vs if both available.

G) Inference APIs (optional)
- init_vpvsdens/api/vs_predictor.py: thin wrapper over GeneralWellLogPredictor with template 'standard_shear'.

H) Merge & modification steps (utilize existing code)
0) Create init_vpvsdens/ and subfolders as listed above.
1) COPY vp configs/curves.py, models.py, training.py, validation.py → init_vpvsdens/core/configs/; then MODIFY curves.py (add DTS/VS/RHOB) and training.py (add shear_training + standard_shear).
2) COPY vp core/loss_functions.py → init_vpvsdens/core/training/losses.py; then MODIFY to add optional create_shear_loss.
3) CREATE init_vpvsdens/core/training/checkpoint.py by adapting robust save/load from init_density_base/core/utils.py; UPDATE manager to use it (either copy manager.py and switch import, or inject at call sites).
4) OPTIONAL: COPY EarlyStopping/metrics helpers into init_vpvsdens/core/training/ for local use.
5) OPTIONAL: ADD init_vpvsdens/api/vs_predictor.py (thin wrapper).
6) Leave MWLT architecture untouched; import transformer/decoder/dataset/normalizer from original modules if not modified to avoid duplication.

I) Validation checklist
- AC outputs within [~40,400] μs/ft; DTS within [~100,1000] μs/ft; DEN within [1.5,3.0] g/cc.
- Acceptable RMSE/R²; adjust mean/std via compute_data_statistics() if scale mismatch appears.
- Robust checkpoints save/load on Windows; histories serialized.

J) Optional phase‑2: Joint training (multi-output)
- Use MultiCurveDecoder + MultiCurveLossBalancer; add gentle Vp/Vs ratio penalty.

Milestones & timeline
- Day 1: Scaffold init_vpvsdens/; copy configs; add DTS/VS/RHOB; add shear_training; add checkpoint.py; smoke test AC & DEN.
- Day 2: Train DTS; tune stats/hyperparams; add vs_predictor wrapper if useful.
- Day 3: Document examples; finalize templates; prepare optional joint training.

Risks and mitigations
- DTS scale/noise: recompute stats, consider Huber, increase patience; gradient clipping.
- DEN activation: prefer raw output + physics clamp if Sigmoid limits learning.
- Import drift: keep unchanged modules imported from originals; test import paths.

Appendix A: Suggested initial normalization params
- AC/VP: zscore mean=200, std=75, physics_range (40,400), activation='none'
- DTS/VS: zscore mean=400, std=200, physics_range (100,1000), activation='none'
- DEN/RHOB: zscore mean=2.5, std=0.3, physics_range (1.5,3.0), activation='sigmoid' (optional) or 'none'

Appendix B: Example single‑target training (conceptual)
```bash path=null start=null
# AC (Vp) using copied configs inside init_vpvsdens
python -m some.training.entry \
  --config_dir ./init_vpvsdens/core/configs --template standard_vp \
  --train_file A1.hdf5 --val_file A1.hdf5 --test_file A2.hdf5 \
  --save_path ./results_vp

# DTS (Vs)
python -m some.training.entry \
  --config_dir ./init_vpvsdens/core/configs --template standard_shear \
  --train_file A1.hdf5 --val_file A1.hdf5 --test_file A2.hdf5 \
  --save_path ./results_vs

# DEN (Rho)
python init_density_base/density_prediction_improved.py \
  --train_file A1.hdf5 --val_file A1.hdf5 --test_file A2.hdf5 \
  --save_path ./results_den --model_type base
```

Notes on PyTorch architecture (Context7)
- No change required to nn components; current code already uses Conv1d, LayerNorm, MultiheadAttention-compatible blocks.
- Keep feature_dim divisible by attention_heads per standard transformer constraints.

Summary
- By creating init_vpvsdens/ and placing all modified configs, losses, and checkpoint IO there (while importing unchanged modules from existing code), we specialize for Vp, Vs, and density with minimal disruption. Start single-target per curve, validate on A1/A2, then optionally progress to joint multi-output with light physics coupling.

