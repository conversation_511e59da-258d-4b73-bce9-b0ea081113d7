"""
Standalone VP (Velocity Prediction) Module
==========================================

A self-contained module for VP prediction using transformer models.
This module includes all necessary components for training, validation, and prediction.

Main Components:
- train_vp_improved.py: Main training and prediction pipeline
- vp_model_improved.py: VP-specific model architectures
- model.py: Base transformer components
- utils.py: Utility functions
- las_processor.py: Data processing utilities
- dataset.py: Dataset handling
- test_vp_pipeline.py: Testing and validation

Usage:
    python train_vp_improved.py

Requirements:
    See requirements.txt for dependencies
"""

__version__ = "1.0.0"
__author__ = "MWLT System"

# Import main components for easy access
try:
    from .train_vp_improved import (
        VpPipelineConfig, VpDataProcessor, VpTrainingManager,
        VpValidationManager, VpPredictionManager, VpPredictionPipeline
    )
    from .vp_model_improved import (
        MWLT_Vp_Small, MWLT_Vp_Base, MWLT_Vp_Large, 
        VpDataNormalizer, VpLoss
    )
    from .utils import get_device, save_checkpoint, EarlyStopping, cal_RMSE, cal_R2
    from .las_processor import LASProcessor
    from .model import (
        Input_Embedding, PositionalEncoding, TransformerBlock, 
        ResCNN, MWL_Transformer, MWLT_Small, MWLT_Base, MWLT_Large
    )
except ImportError:
    # Fallback for when imports fail
    pass

__all__ = [
    # Main pipeline components
    'VpPipelineConfig', 'VpDataProcessor', 'VpTrainingManager',
    'VpValidationManager', 'VpPredictionManager', 'VpPredictionPipeline',
    
    # Model components
    'MWLT_Vp_Small', 'MWLT_Vp_Base', 'MWLT_Vp_Large',
    'VpDataNormalizer', 'VpLoss',
    
    # Base components
    'Input_Embedding', 'PositionalEncoding', 'TransformerBlock',
    'ResCNN', 'MWL_Transformer', 'MWLT_Small', 'MWLT_Base', 'MWLT_Large',
    
    # Utilities
    'get_device', 'save_checkpoint', 'EarlyStopping', 'cal_RMSE', 'cal_R2',
    'LASProcessor'
]
