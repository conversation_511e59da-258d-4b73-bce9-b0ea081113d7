"""
Robust checkpoint saving and loading functionality for General Well Log Transformer

Adapted from init_density_base/core/utils.py with improvements for Windows compatibility
and PyTorch version robustness.
"""
import torch
import numpy as np
import os
import stat
from typing import Dict, Any, Optional, Tuple, Union
import logging

logger = logging.getLogger(__name__)


def get_device(device_id: int = 0) -> torch.device:
    """
    Get the appropriate device (GPU if available, otherwise CPU)
    
    Args:
        device_id: GPU device ID to use (default: 0)

    Returns:
        torch.device: The device to use for computation
    """
    if torch.cuda.is_available():
        device = torch.device(f"cuda:{device_id}")
        logger.info(f"CUDA is available. Using GPU: {device}")
        logger.info(f"GPU Name: {torch.cuda.get_device_name(device_id)}")
        logger.info(f"GPU Memory: {torch.cuda.get_device_properties(device_id).total_memory / 1024**3:.1f} GB")
    else:
        device = torch.device("cpu")
        logger.info("CUDA is not available. Using CPU for computation.")

    return device


def save_checkpoint(
    state: Dict[str, Any], 
    path: str,
    create_backup: bool = True
) -> bool:
    """
    Save checkpoint during training with improved error handling
    
    Args:
        state: Dictionary containing model state, epoch, loss, etc.
        path: Save path
        create_backup: Whether to create a backup of existing checkpoint

    Returns:
        bool: True if successful, False otherwise
    """
    logger.info(f"Saving checkpoint to: {path}")

    try:
        # Ensure the directory exists
        directory = os.path.dirname(path)
        if directory and not os.path.exists(directory):
            logger.info(f"Creating directory: {directory}")
            os.makedirs(directory, exist_ok=True)

        # Create backup of existing file if requested
        if create_backup and os.path.exists(path):
            backup_path = path + '.backup'
            try:
                os.rename(path, backup_path)
                logger.info(f"Created backup: {backup_path}")
            except Exception as backup_error:
                logger.warning(f"Failed to create backup: {backup_error}")

        # Check if directory is writable
        if directory and os.path.exists(directory):
            if not os.access(directory, os.W_OK):
                logger.warning(f"Directory {directory} is not writable. Attempting to fix permissions...")
                try:
                    # Try to remove read-only attribute on Windows
                    if os.name == 'nt':  # Windows
                        os.chmod(directory, stat.S_IWRITE | stat.S_IREAD)
                    else:  # Unix-like systems
                        os.chmod(directory, 0o755)
                    logger.info("Directory permissions updated")
                except Exception as perm_error:
                    logger.error(f"Failed to update directory permissions: {perm_error}")
                    raise

        # Save the checkpoint
        torch.save(state, path)
        logger.info("Checkpoint saved successfully")
        
        # Remove backup if save was successful
        backup_path = path + '.backup'
        if create_backup and os.path.exists(backup_path):
            try:
                os.remove(backup_path)
            except Exception as cleanup_error:
                logger.warning(f"Failed to remove backup file: {cleanup_error}")
        
        return True

    except Exception as e:
        logger.error(f"Error saving checkpoint: {e}")
        logger.error(f"Path: {path}")
        logger.error(f"Directory exists: {os.path.exists(os.path.dirname(path)) if os.path.dirname(path) else 'N/A'}")
        logger.error(f"Directory writable: {os.access(os.path.dirname(path), os.W_OK) if os.path.dirname(path) and os.path.exists(os.path.dirname(path)) else 'N/A'}")
        
        # Try to restore backup if it exists
        backup_path = path + '.backup'
        if create_backup and os.path.exists(backup_path):
            try:
                os.rename(backup_path, path)
                logger.info("Restored backup after failed save")
            except Exception as restore_error:
                logger.error(f"Failed to restore backup: {restore_error}")
        
        return False


def load_checkpoint(
    path: str, 
    device: Optional[torch.device] = None
) -> Tuple[Optional[Dict[str, Any]], Optional[int], Optional[float]]:
    """
    Load checkpoint with automatic device detection and robust error handling
    
    Args:
        path: Checkpoint path
        device: Target device (if None, auto-detect GPU/CPU)

    Returns:
        Tuple of (model_dict, epoch, loss). Returns (None, None, None) on failure.
    """
    if not os.path.exists(path):
        logger.error(f"Checkpoint file not found: {path}")
        return None, None, None
    
    if device is None:
        # Auto-detect device: use GPU if available, otherwise CPU
        if torch.cuda.is_available():
            device = torch.device("cuda")
            logger.info(f"Loading checkpoint to GPU: {device}")
        else:
            device = torch.device("cpu")
            logger.info(f"Loading checkpoint to CPU: {device}")
    else:
        logger.info(f"Loading checkpoint to specified device: {device}")

    logger.info(f"PyTorch version: {torch.__version__}")
    
    try:
        # First attempt: modern PyTorch with explicit weights_only=False
        logger.info("Attempting torch.load(weights_only=False)...")
        checkpoint = torch.load(path, map_location=device, weights_only=False)
        
    except TypeError:
        # Older PyTorch without weights_only parameter
        logger.info("weights_only parameter not supported. Falling back to torch.load without it...")
        try:
            checkpoint = torch.load(path, map_location=device)
        except Exception as e:
            logger.error(f"Legacy load failed: {e}")
            return None, None, None
            
    except Exception as e:
        # If safe-unpickler blocked a global, try allowlisting numpy and loading safely
        logger.warning(f"First load attempt failed: {e}")
        try:
            add_safe_globals = getattr(torch.serialization, 'add_safe_globals', None)
            if add_safe_globals is None:
                raise e
            
            # Allowlist numpy reconstruct if present
            import numpy.core.multiarray as _np_multiarray
            logger.info("Allowlisting numpy.core.multiarray._reconstruct and retrying with weights_only=True...")
            add_safe_globals([_np_multiarray._reconstruct])
            checkpoint = torch.load(path, map_location=device, weights_only=True)
            
        except Exception as e2:
            logger.error(f"Safe load attempt also failed: {e2}")
            return None, None, None
    
    # Support both formats: {'model_state_dict', 'epoch', 'loss'} or a raw state_dict
    if isinstance(checkpoint, dict) and "model_state_dict" in checkpoint:
        model_dict = checkpoint["model_state_dict"]
        epoch = checkpoint.get("epoch")
        loss = checkpoint.get("loss")
        logger.info(f"Loaded checkpoint with epoch: {epoch}, loss: {loss}")
    else:
        # Assume it's a raw state_dict
        model_dict = checkpoint
        epoch = None
        loss = None
        logger.info("Loaded raw state_dict (no epoch/loss info)")
        
    return model_dict, epoch, loss


def save_training_history(
    history: Dict[str, list],
    path: str
) -> bool:
    """
    Save training history (losses, metrics) to file
    
    Args:
        history: Dictionary containing training history
        path: Save path
        
    Returns:
        bool: True if successful
    """
    try:
        torch.save(history, path)
        logger.info(f"Training history saved to: {path}")
        return True
    except Exception as e:
        logger.error(f"Failed to save training history: {e}")
        return False


def load_training_history(
    path: str
) -> Optional[Dict[str, list]]:
    """
    Load training history from file
    
    Args:
        path: History file path
        
    Returns:
        Training history dictionary or None if failed
    """
    if not os.path.exists(path):
        logger.warning(f"History file not found: {path}")
        return None
        
    try:
        history = torch.load(path, map_location='cpu')
        logger.info(f"Training history loaded from: {path}")
        return history
    except Exception as e:
        logger.error(f"Failed to load training history: {e}")
        return None


class CheckpointManager:
    """
    Manager class for handling checkpoints throughout training
    """
    
    def __init__(
        self, 
        save_dir: str,
        model_name: str = "model",
        keep_best: bool = True,
        keep_last_n: int = 3
    ):
        """
        Initialize checkpoint manager
        
        Args:
            save_dir: Directory to save checkpoints
            model_name: Base name for checkpoint files
            keep_best: Whether to keep the best checkpoint
            keep_last_n: Number of recent checkpoints to keep
        """
        self.save_dir = save_dir
        self.model_name = model_name
        self.keep_best = keep_best
        self.keep_last_n = keep_last_n
        
        self.best_loss = float('inf')
        self.best_checkpoint_path = None
        self.recent_checkpoints = []
        
        # Ensure save directory exists
        os.makedirs(save_dir, exist_ok=True)
        
    def save_checkpoint(
        self,
        state: Dict[str, Any],
        epoch: int,
        is_best: bool = False
    ) -> bool:
        """
        Save checkpoint with automatic cleanup
        
        Args:
            state: Model state dictionary
            epoch: Current epoch
            is_best: Whether this is the best checkpoint so far
            
        Returns:
            bool: True if successful
        """
        # Save regular checkpoint
        checkpoint_path = os.path.join(self.save_dir, f"{self.model_name}_epoch_{epoch}.pt")
        success = save_checkpoint(state, checkpoint_path)
        
        if not success:
            return False
            
        # Track recent checkpoints
        self.recent_checkpoints.append(checkpoint_path)
        
        # Save best checkpoint if needed
        if self.keep_best and is_best:
            best_path = os.path.join(self.save_dir, f"{self.model_name}_best.pt")
            save_checkpoint(state, best_path, create_backup=False)
            self.best_checkpoint_path = best_path
            self.best_loss = state.get('loss', float('inf'))
            
        # Cleanup old checkpoints
        self._cleanup_checkpoints()
        
        return True
        
    def _cleanup_checkpoints(self):
        """Remove old checkpoints keeping only the most recent ones"""
        if len(self.recent_checkpoints) > self.keep_last_n:
            # Remove oldest checkpoints
            to_remove = self.recent_checkpoints[:-self.keep_last_n]
            for path in to_remove:
                try:
                    if os.path.exists(path):
                        os.remove(path)
                        logger.info(f"Removed old checkpoint: {path}")
                except Exception as e:
                    logger.warning(f"Failed to remove old checkpoint {path}: {e}")
                    
            # Update the list
            self.recent_checkpoints = self.recent_checkpoints[-self.keep_last_n:]
            
    def load_best_checkpoint(self, device: Optional[torch.device] = None):
        """Load the best checkpoint"""
        if self.best_checkpoint_path and os.path.exists(self.best_checkpoint_path):
            return load_checkpoint(self.best_checkpoint_path, device)
        else:
            logger.warning("No best checkpoint available")
            return None, None, None
            
    def load_latest_checkpoint(self, device: Optional[torch.device] = None):
        """Load the most recent checkpoint"""
        if self.recent_checkpoints:
            latest_path = self.recent_checkpoints[-1]
            if os.path.exists(latest_path):
                return load_checkpoint(latest_path, device)
        
        logger.warning("No recent checkpoint available")
        return None, None, None


# Backward compatibility functions
def cal_RMSE(pred: np.ndarray, real: np.ndarray) -> float:
    """Calculate RMSE between predictions and real values"""
    MSE = np.mean((pred - real) ** 2)
    RMSE = np.sqrt(MSE)
    return RMSE


def cal_R2(pred: np.ndarray, real: np.ndarray) -> float:
    """Calculate R-squared between predictions and real values"""
    SSR = sum((real - pred) ** 2)
    SST = sum((real - np.mean(real)) ** 2)
    r2 = 1 - SSR / SST
    return r2
