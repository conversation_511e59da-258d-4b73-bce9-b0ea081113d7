"""
Generalized decoder architectures for multi-curve well log prediction

This module extends the original VpDecoder to support multiple output curves
with configurable activation functions and curve-specific processing.
"""

import torch
import torch.nn as nn
from typing import Dict, List, Optional, Union
from ..configs import get_curve_config, ACTIVATION_FUNCTIONS


class GeneralDecoder(nn.Module):
    """
    Generalized decoder for multi-curve well log prediction
    
    Extends the original VpDecoder to support:
    - Multiple output curves with configurable channels
    - Curve-specific activation functions 
    - Flexible output processing based on curve properties
    
    This replaces hardcoded single-output Vp-specific architecture
    """
    
    def __init__(self, 
                 res_num: int = 4, 
                 feature_num: int = 64,
                 output_curves: List[str] = None,
                 activation_types: Dict[str, str] = None,
                 out_channels: int = None):
        """
        Initialize GeneralDecoder
        
        Args:
            res_num: Number of ResNet blocks
            feature_num: Feature dimension
            output_curves: List of output curve names (e.g., ['AC', 'DEN', 'CNL'])
            activation_types: Dict mapping curves to activation types
                             {'AC': 'none', 'DEN': 'sigmoid', 'CNL': 'relu'}
            out_channels: Total output channels (alternative to output_curves)
        """
        super().__init__()
        
        # Handle backward compatibility
        if output_curves is None:
            if out_channels is None:
                out_channels = 1  # Default single output for backward compatibility
            output_curves = ['VP']  # Default Vp output
            
        self.output_curves = output_curves
        self.num_output_curves = len(output_curves) if out_channels is None else out_channels
        
        # Get activation types from curve configurations if not provided
        if activation_types is None:
            activation_types = {}
            for curve in output_curves:
                try:
                    curve_config = get_curve_config(curve)
                    activation_types[curve] = curve_config.get('activation', 'none')
                except ValueError:
                    # Unknown curve, use no activation
                    activation_types[curve] = 'none'
        
        self.activation_types = activation_types
        
        # ResNet processing blocks (same as original)
        self.rescnn = nn.ModuleList([
            self._create_rescnn_block(feature_num) 
            for _ in range(res_num)
        ])
        
        # Shared feature processing
        self.shared_layers = nn.Sequential(
            nn.Conv1d(in_channels=feature_num, out_channels=feature_num//2,
                     kernel_size=11, padding=5, stride=1),
            nn.ReLU(),
            nn.Dropout(0.1)
        )
        
        # Curve-specific output heads
        self.output_heads = nn.ModuleDict()
        for i, curve in enumerate(output_curves):
            self.output_heads[curve] = self._create_output_head(
                feature_num//2, 1, activation_types.get(curve, 'none')
            )
        
        # Alternative: single output head for backward compatibility
        if out_channels is not None and output_curves == ['VP']:
            self.single_output_head = nn.Conv1d(
                in_channels=feature_num//2, out_channels=out_channels,
                kernel_size=11, padding=5, stride=1
            )
    
    def _create_rescnn_block(self, feature_num: int):
        """Create a single ResNet CNN block"""
        from ..model import ResCNN  # Import from existing model
        return ResCNN(in_channels=feature_num, out_channels=feature_num,
                     kernel_size=11, padding=5, stride=1)
    
    def _create_output_head(self, in_channels: int, out_channels: int, activation_type: str):
        """
        Create curve-specific output head with appropriate activation
        
        Args:
            in_channels: Input feature dimension
            out_channels: Output channels (typically 1 per curve)
            activation_type: Type of activation function
            
        Returns:
            nn.Module: Output head with appropriate activation
        """
        layers = [
            nn.Conv1d(in_channels=in_channels, out_channels=out_channels,
                     kernel_size=11, padding=5, stride=1)
        ]
        
        # Add activation function based on curve requirements
        if activation_type == 'sigmoid':
            layers.append(nn.Sigmoid())
        elif activation_type == 'relu':
            layers.append(nn.ReLU())
        elif activation_type == 'tanh':
            layers.append(nn.Tanh())
        elif activation_type == 'softplus':
            layers.append(nn.Softplus())
        # 'none' or unknown types get no activation (raw output)
        
        return nn.Sequential(*layers)
    
    def forward(self, x: torch.Tensor) -> Union[torch.Tensor, Dict[str, torch.Tensor]]:
        """
        Forward pass through decoder
        
        Args:
            x: Input features [B, feature_num, L]
            
        Returns:
            For single output: torch.Tensor [B, out_channels, L]
            For multi-output: Dict[str, torch.Tensor] mapping curve names to outputs
        """
        # Apply ResNet blocks (same as original)
        for rescnn_block in self.rescnn:
            x = rescnn_block(x)
        
        # Apply shared processing
        x = self.shared_layers(x)
        
        # Handle single output for backward compatibility
        if len(self.output_curves) == 1 and hasattr(self, 'single_output_head'):
            return self.single_output_head(x)
        
        # Multi-curve output
        outputs = {}
        for curve in self.output_curves:
            outputs[curve] = self.output_heads[curve](x)
        
        # Return single tensor if only one output curve for backward compatibility
        if len(self.output_curves) == 1:
            return outputs[self.output_curves[0]]
            
        return outputs
    
    def get_output_info(self) -> Dict[str, Dict]:
        """
        Get information about output curves and their configurations
        
        Returns:
            dict: Information about each output curve
        """
        info = {}
        for curve in self.output_curves:
            try:
                curve_config = get_curve_config(curve)
                info[curve] = {
                    'name': curve_config['name'],
                    'unit': curve_config['unit'],
                    'activation': self.activation_types.get(curve, 'none'),
                    'physics_range': curve_config['physics_range']
                }
            except ValueError:
                info[curve] = {
                    'name': curve,
                    'unit': 'unknown',
                    'activation': self.activation_types.get(curve, 'none'),
                    'physics_range': None
                }
        return info


class VpCompatibleDecoder(GeneralDecoder):
    """
    Backward compatible decoder that mimics VpDecoder behavior
    
    This ensures existing VpTransformer models continue to work
    while using the new GeneralDecoder infrastructure.
    """
    
    def __init__(self, res_num: int = 4, out_channels: int = 1, feature_num: int = 64):
        """
        Initialize backward compatible VpDecoder
        
        Args:
            res_num: Number of ResNet blocks
            out_channels: Output channels (should be 1 for Vp)
            feature_num: Feature dimension
        """
        # Initialize as single Vp output with no activation constraints
        super().__init__(
            res_num=res_num,
            feature_num=feature_num,
            output_curves=['VP'],
            activation_types={'VP': 'none'},  # No artificial bounds like original VpDecoder
            out_channels=out_channels
        )
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        Forward pass maintaining VpDecoder interface
        
        Args:
            x: Input features [B, feature_num, L]
            
        Returns:
            torch.Tensor: Vp predictions [B, 1, L]
        """
        result = super().forward(x)
        # Ensure we return a tensor, not a dict
        if isinstance(result, dict):
            return result['VP']
        return result


class MultiCurveDecoder(GeneralDecoder):
    """
    Specialized decoder for multi-curve prediction scenarios
    
    Provides enhanced features for handling multiple output curves
    including curve relationship modeling and joint optimization.
    """
    
    def __init__(self, 
                 output_curves: List[str],
                 res_num: int = 4,
                 feature_num: int = 64,
                 use_curve_relationships: bool = True):
        """
        Initialize multi-curve decoder
        
        Args:
            output_curves: List of output curve names
            res_num: Number of ResNet blocks
            feature_num: Feature dimension
            use_curve_relationships: Whether to model relationships between curves
        """
        # Get activation types from curve configurations
        activation_types = {}
        for curve in output_curves:
            try:
                curve_config = get_curve_config(curve)
                activation_types[curve] = curve_config.get('activation', 'none')
            except ValueError:
                activation_types[curve] = 'none'
        
        super().__init__(
            res_num=res_num,
            feature_num=feature_num,
            output_curves=output_curves,
            activation_types=activation_types
        )
        
        self.use_curve_relationships = use_curve_relationships
        
        # Add curve relationship modeling if requested
        if use_curve_relationships and len(output_curves) > 1:
            self.relationship_layer = nn.MultiheadAttention(
                embed_dim=feature_num//2,
                num_heads=4,
                batch_first=True
            )
    
    def forward(self, x: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        Forward pass with optional curve relationship modeling
        
        Args:
            x: Input features [B, feature_num, L]
            
        Returns:
            Dict[str, torch.Tensor]: Mapping curve names to predictions
        """
        # Apply ResNet blocks
        for rescnn_block in self.rescnn:
            x = rescnn_block(x)
        
        # Apply shared processing
        shared_features = self.shared_layers(x)
        
        # Model curve relationships if enabled
        if self.use_curve_relationships and len(self.output_curves) > 1:
            # Transpose for attention: [B, L, C]
            attn_input = shared_features.transpose(1, 2)
            enhanced_features, _ = self.relationship_layer(
                attn_input, attn_input, attn_input
            )
            # Transpose back: [B, C, L]
            shared_features = enhanced_features.transpose(1, 2)
        
        # Generate outputs for each curve
        outputs = {}
        for curve in self.output_curves:
            outputs[curve] = self.output_heads[curve](shared_features)
        
        return outputs
    
    def get_curve_relationships(self) -> Optional[torch.Tensor]:
        """
        Get learned curve relationship weights if available
        
        Returns:
            torch.Tensor: Attention weights showing curve relationships
        """
        if hasattr(self, 'relationship_layer'):
            # This would require storing attention weights during forward pass
            # Implementation depends on specific use case requirements
            pass
        return None


# Factory function for creating decoders
def create_decoder(decoder_type: str = 'general', **kwargs) -> nn.Module:
    """
    Factory function for creating different types of decoders
    
    Args:
        decoder_type: Type of decoder ('general', 'vp_compatible', 'multi_curve')
        **kwargs: Arguments passed to decoder constructor
        
    Returns:
        nn.Module: Decoder instance
    """
    if decoder_type == 'general':
        return GeneralDecoder(**kwargs)
    elif decoder_type == 'vp_compatible':
        return VpCompatibleDecoder(**kwargs)
    elif decoder_type == 'multi_curve':
        return MultiCurveDecoder(**kwargs)
    else:
        raise ValueError(f"Unknown decoder type: {decoder_type}")


# Backward compatibility aliases
VpDecoder = VpCompatibleDecoder  # Exact replacement for original VpDecoder