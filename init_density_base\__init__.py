"""
Initial Density Prediction Module

A self-contained density prediction module that consolidates all components 
of the MWLT Transformer density prediction system.

This module provides:
- Enhanced density prediction with proper normalization
- Comprehensive visualization and plotting capabilities
- Multiple model architectures (Small, Base, Large)
- Integrated training and testing pipelines

Main Classes:
    DensityDataNormalizer: Handles proper normalization for density prediction
    ImprovedDensityDataset: Enhanced dataset class for density prediction
    DensityPlotterImproved: Comprehensive plotting and visualization
    MWLT_Small, MWLT_Base, MWLT_Large: Model architectures

Example Usage:
    >>> from density_prediction_improved import DensityDataNormalizer, ImprovedDensityDataset
    >>> from density_prediction_plot_improved import DensityPlotterImproved
    >>> from core.model import MWLT_Small, MWLT_Base, MWLT_Large
    >>> from core.utils import get_device, load_checkpoint
    >>> 
    >>> # Create dataset
    >>> normalizer = DensityDataNormalizer()
    >>> dataset = ImprovedDensityDataset(
    ...     file_path="data.hdf5",
    ...     input_curves=['GR', 'AC', 'CNL', 'RLLD'],
    ...     output_curves=['DENSITY'],
    ...     normalizer=normalizer
    ... )
    >>> 
    >>> # Create model
    >>> model = MWLT_Small(in_channels=4, out_channels=1, seq_len=640)
    >>> 
    >>> # Create plotter
    >>> plotter = DensityPlotterImproved("results_dir")
"""

__version__ = "1.0.0"
__author__ = "MWLT Team"
__email__ = "<EMAIL>"

# Import main classes and functions
try:
    from density_prediction_improved import (
        DensityDataNormalizer,
        ImprovedDensityDataset,
        create_density_dataset
    )
    
    from density_prediction_plot_improved import DensityPlotterImproved
    
    from core.model import (
        MWLT_Small,
        MWLT_Base, 
        MWLT_Large,
        MWL_Transformer
    )
    
    from core.utils import (
        get_device,
        save_checkpoint,
        load_checkpoint,
        EarlyStopping,
        cal_RMSE,
        cal_R2
    )
    
    from core.dataset import WellDataset
    
except ImportError as e:
    print(f"Warning: Some imports failed in initial_density module: {e}")
    print("This may be due to missing dependencies. Please ensure all required packages are installed.")

# Define what gets imported with "from initial_density import *"
__all__ = [
    # Core classes
    'DensityDataNormalizer',
    'ImprovedDensityDataset', 
    'DensityPlotterImproved',
    
    # Model architectures
    'MWLT_Small',
    'MWLT_Base',
    'MWLT_Large',
    'MWL_Transformer',
    
    # Utility functions
    'get_device',
    'save_checkpoint',
    'load_checkpoint',
    'EarlyStopping',
    'cal_RMSE',
    'cal_R2',
    
    # Dataset classes
    'WellDataset',
    
    # Convenience functions
    'create_density_dataset',
]
