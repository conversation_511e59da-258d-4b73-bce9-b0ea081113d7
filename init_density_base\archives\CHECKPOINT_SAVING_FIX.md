# PyTorch Checkpoint Saving Fix

## Problem Summary

The PyTorch model training pipeline was failing with a `RuntimeError` when trying to save checkpoints during training:

```
RuntimeError: File ../density_improved_results\best_density_improved_model.pth cannot be opened.
```

### Root Cause Analysis

1. **Directory Permission Issue**: The `../density_improved_results/` directory had a **read-only attribute** set by OneDrive synchronization
2. **Path Separator Confusion**: The error message showed mixed path separators (backslash vs forward slash)
3. **Insufficient Error Handling**: The original `save_checkpoint()` function lacked detailed error reporting
4. **OneDrive Sync Conflicts**: OneDrive can sometimes lock directories or set restrictive permissions

### Investigation Results

- Directory existed: ✅ `../density_improved_results/` was present
- Files existed: ✅ Previous checkpoint files were there
- Permission issue: ❌ Directory had read-only attribute (`R` flag)
- Write access: ❌ `os.access(directory, os.W_OK)` returned `False`

## Solution Implemented

### 1. Enhanced `save_checkpoint()` Function (`utils.py`)

**Before:**
```python
def save_checkpoint(state, path):
    print("saving checkpoint")
    torch.save(state, path)
```

**After:**
```python
def save_checkpoint(state, path):
    import os
    import stat
    
    print(f"Saving checkpoint to: {path}")
    
    try:
        # Ensure the directory exists
        directory = os.path.dirname(path)
        if directory and not os.path.exists(directory):
            print(f"Creating directory: {directory}")
            os.makedirs(directory, exist_ok=True)
        
        # Check if directory is writable
        if directory and os.path.exists(directory):
            if not os.access(directory, os.W_OK):
                print(f"Warning: Directory {directory} is not writable. Attempting to fix permissions...")
                try:
                    # Try to remove read-only attribute on Windows
                    if os.name == 'nt':  # Windows
                        os.chmod(directory, stat.S_IWRITE | stat.S_IREAD)
                    else:  # Unix-like systems
                        os.chmod(directory, 0o755)
                    print("Directory permissions updated")
                except Exception as perm_error:
                    print(f"Failed to update directory permissions: {perm_error}")
                    raise
        
        # Save the checkpoint
        torch.save(state, path)
        print("Checkpoint saved successfully")
        
    except Exception as e:
        print(f"Error saving checkpoint: {e}")
        print(f"Path: {path}")
        print(f"Directory exists: {os.path.exists(os.path.dirname(path)) if os.path.dirname(path) else 'N/A'}")
        print(f"Directory writable: {os.access(os.path.dirname(path), os.W_OK) if os.path.dirname(path) and os.path.exists(os.path.dirname(path)) else 'N/A'}")
        raise
```

### 2. Improved Directory Creation (`density_prediction_improved.py`)

Added `_create_save_directory()` method with:
- **Permission checking and fixing**
- **Write tests to verify directory accessibility**
- **Fallback mechanism to local directory**
- **Detailed error reporting**

### 3. Immediate Fix Applied

```bash
# Removed read-only attribute from the directory
attrib -R "../density_improved_results"
```

## Key Improvements

### 1. Automatic Permission Fixing
- Detects read-only directories
- Automatically removes read-only attributes on Windows
- Sets proper permissions (755) on Unix-like systems

### 2. Enhanced Error Reporting
- Shows exact file paths being used
- Reports directory existence and write permissions
- Provides detailed error messages for debugging

### 3. Fallback Mechanism
- If OneDrive directory has persistent issues, falls back to local directory
- Ensures training can continue even with OneDrive sync problems

### 4. Proactive Directory Management
- Creates directories if they don't exist
- Tests write access before attempting to save
- Provides clear feedback about directory status

## Testing Results

All tests pass successfully:

```
✅ Basic checkpoint saving successful
✅ Checkpoint loading verification successful
✅ EarlyStopping saved best model at epoch 3 with loss 0.400
✅ OneDrive path checkpoint saving successful
✅ Test directory is writable
✅ File write test successful
```

## Prevention Measures

### 1. For Users
- **Monitor OneDrive sync status** - Ensure OneDrive isn't locking directories
- **Check directory permissions** before training
- **Use local directories** for intensive I/O operations if OneDrive causes issues

### 2. For Code
- **Robust error handling** in all file I/O operations
- **Permission checking** before attempting writes
- **Fallback mechanisms** for critical operations

## Usage

The fix is automatically applied. No changes needed to existing training scripts. The enhanced error handling will:

1. **Automatically detect and fix** permission issues
2. **Provide clear error messages** if problems persist
3. **Fall back to local directory** if OneDrive path is problematic
4. **Continue training** without interruption

## Files Modified

1. `utils.py` - Enhanced `save_checkpoint()` function
2. `density_prediction_improved.py` - Added `_create_save_directory()` method
3. `test_checkpoint_fix.py` - Comprehensive test suite (new file)

## Verification

Run the test script to verify the fix:

```bash
python test_checkpoint_fix.py
```

This will test all checkpoint saving scenarios and confirm the fix is working properly.
