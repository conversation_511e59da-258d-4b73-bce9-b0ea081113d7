# MWLT Generalized Well Log Prediction

A unified, configuration-driven transformer pipeline for predicting well log curves (Vp/AC, density, neutron porosity, gamma ray, resistivity, and more). This repository currently contains two working paths and a plan to consolidate them into a new generalized package.

- init_vp_pred: Vp (sonic velocity) prediction with modular components (transformer, dataset, normalizer, loss, trainer, configs, API, LAS/HDF5 I/O)
- init_density_base: Density prediction with a cleaned MWLT backbone and robust checkpoint utilities
- Planned: init_generalization: New generalized package consolidating reusable modules to support any curve via configuration

See 1_Generalization_Architecture.md for the full consolidation plan and file mapping.

## Features
- MWLT backbone (Conv1d + ResCNN → Transformer → Decoder)
- Per-curve normalization, physics ranges, and activation selection via configs
- Generalized training manager with early stopping, schedulers, gradient clipping, optional AMP
- Robust checkpointing with safe loading (incl. PyTorch 2.6+ compatibility)
- LAS/HDF5 processing and simple API for inference
- Backward compatibility for existing Vp and Density flows

## Repository layout (current)
- init_vp_pred/
  - vp_predictor/
    - core/: transformer, decoder, dataset, normalizer, training manager, losses
    - configs/: curves, models, training, validation
    - api/: predictor (general + legacy wrappers)
    - utils.py, las_processor.py
  - training/inference scripts, tests, docs
- init_density_base/
  - core/: model (MWLT), dataset, utils (robust checkpoint)
  - density_prediction_improved.py, plotting, archives
- 1_Generalization_Architecture.md: generalization plan and step-by-step migration

## Planned new package (init_generalization)
The generalization effort will create a new top-level init_generalization/ folder with:
- core/model: transformer.py, decoder.py, embeddings.py, attention.py
- core/data: dataset.py, normalizer.py, io_las.py
- core/training: manager.py, losses.py, metrics.py, early_stopping.py, checkpoint.py
- core/configs: curves.py, models.py, training.py, validation.py
- api: predictor.py (general), vp_predictor.py, density_predictor.py
- pipelines: train.py, evaluate.py, infer.py
- tests: unit and tiny end-to-end samples

Details are in 1_Generalization_Architecture.md (source → destination mapping and migration steps).

## Quick start (existing paths)
- Run improved density pipeline
  ```bash path=null start=null
  # From repository root
  python init_density_base/density_prediction_improved.py \
    --train_file A1.hdf5 --val_file A1.hdf5 --test_file A2.hdf5 \
    --save_path density_improved_results --model_type base --epochs 100
  ```
- Vp testing and training (see VP guides for more)
  ```bash path=null start=null
  # Example: quick Vp test flow (refer to VP_TRAINING_GUIDE / VP_PREDICTION_GUIDE inside init_vp_pred)
  python init_vp_pred/train_vp_improved.py
  python init_vp_pred/test_vp_pipeline.py
  ```

## Quick start (after init_generalization scaffold)
- Train Vp (AC) with a standard template
  ```bash path=null start=null
  python -m init_generalization.pipelines.train --template standard_vp \
    --train_file A1.hdf5 --val_file A1.hdf5 --test_file A2.hdf5 \
    --save_path ./results_vp
  ```
- Train Density with a standard template
  ```bash path=null start=null
  python -m init_generalization.pipelines.train --template standard_density \
    --train_file A1.hdf5 --val_file A1.hdf5 --test_file A2.hdf5 \
    --save_path ./results_den
  ```
- Predict via API
  ```python path=null start=null
  from init_generalization.api.predictor import GeneralWellLogPredictor

  predictor = GeneralWellLogPredictor(
      template_name='vp_prediction',
      model_path='./results_vp/best_model.pth'
  )

  result = predictor.predict({
      'GR': gr_array,
      'CNL': cnl_array,
      'DEN': den_array,
      'RLLD': rlld_array
  })
  vp = result['predictions']
  ```

## Environment & dependencies
- Python 3.9+
- PyTorch (CPU or CUDA)
- numpy, h5py, matplotlib
- Optional: lasio, pandas

Install per-path requirements if present:
```bash path=null start=null
# Examples
pip install -r init_vp_pred/requirements.txt
pip install -r init_density_base/requirements.txt
```

## Roadmap
- Day 1–2: Create init_generalization skeleton; copy generalized modules; fix imports
- Day 3–4: Wire robust checkpoints; add CLI pipelines (train/evaluate/infer)
- Day 5–6: Thin APIs (Vp/Density), A1/A2 smoke tests; tune configs
- Week 2: Tests, CI, documentation; enable multi-output (optional)

## References
- 1_Generalization_Architecture.md (this repo)
- VP_TRAINING_GUIDE and VP_PREDICTION_GUIDE (Warp Drive notebook content)
- PyTorch docs (Context7) for Conv1d, LayerNorm, MultiheadAttention, Dataset/DataLoader

## Contributing
- Use feature branches and PRs
- Keep generalized code path import-clean and well-tested
- Respect backward compatibility where noted

## License
- Provide your project’s license details here.

