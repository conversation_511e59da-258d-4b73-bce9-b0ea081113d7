"""
Legacy API compatibility layer for VpTransformer

This module provides exact backward compatibility with the existing VpPredictor
and VpTransformerAPI while using the new GeneralWellLogTransformer infrastructure.
"""

import torch
import numpy as np
from typing import Dict, List, Optional, Union
from ..core import GeneralWellLogTransformer, GeneralDataNormalizer
from ..configs import get_model_template
from ..utils import get_device, load_checkpoint


class VpPredictor:
    """
    Backward compatible VpPredictor interface
    
    This class maintains 100% compatibility with the original VpPredictor API
    while using the new GeneralWellLogTransformer infrastructure underneath.
    """
    
    def __init__(self, 
                 model_path: str = None,
                 device_id: int = 0, 
                 model_type: str = "base",
                 model: torch.nn.Module = None):
        """
        Initialize VpPredictor (backward compatible)
        
        Args:
            model_path: Path to trained model checkpoint
            device_id: GPU device ID (0 for first GPU, -1 for CPU)
            model_type: Model size ('small', 'base', 'large')
            model: Pre-loaded model (optional)
        """
        self.device = get_device() if device_id >= 0 else torch.device('cpu')
        self.model_type = model_type
        self.model_path = model_path
        
        # Initialize normalizer (backward compatible)
        self.normalizer = GeneralDataNormalizer(
            input_curves=['GR', 'CNL', 'DEN', 'RLLD'],
            output_curves=['VP']
        )
        
        # Load or create model
        if model is not None:
            self.model = model
        elif model_path is not None:
            self.model = self._load_model(model_path)
        else:
            # Create new model (for testing/development)
            self.model = self._create_model()
        
        self.model.to(self.device)
        self.model.eval()
    
    def _create_model(self) -> GeneralWellLogTransformer:
        """Create new model with backward compatible configuration"""
        # Use VpTransformer compatible configuration
        if self.model_type.lower() == 'small':
            from ..core import GWLT_Vp_Small
            return GWLT_Vp_Small()
        elif self.model_type.lower() == 'base':
            from ..core import GWLT_Vp_Base
            return GWLT_Vp_Base()
        elif self.model_type.lower() == 'large':
            from ..core import GWLT_Vp_Large
            return GWLT_Vp_Large()
        else:
            raise ValueError(f"Unknown model type: {self.model_type}")
    
    def _load_model(self, model_path: str) -> GeneralWellLogTransformer:
        """Load model from checkpoint with backward compatibility"""
        try:
            # Try to load with new GeneralWellLogTransformer
            model = self._create_model()
            checkpoint = load_checkpoint(model_path, self.device)
            model.load_state_dict(checkpoint['model_state_dict'])
            return model
        except Exception as e:
            # If that fails, try to create a compatible model
            # This handles cases where old VpTransformer checkpoints are loaded
            print(f"Warning: Could not load as GeneralWellLogTransformer: {e}")
            print("Attempting backward compatibility mode...")
            
            # Create backward compatible model
            from ..core import VpTransformer_Compatible
            model = VpTransformer_Compatible()
            try:
                checkpoint = load_checkpoint(model_path, self.device)
                model.load_state_dict(checkpoint['model_state_dict'])
                return model
            except Exception as e2:
                raise RuntimeError(f"Could not load model: {e2}")
    
    def predict_from_curves(self, 
                           gr: np.ndarray,
                           cnl: np.ndarray,
                           den: np.ndarray,
                           rlld: np.ndarray) -> np.ndarray:
        """
        Predict Vp from individual curve arrays (exact backward compatibility)
        
        Args:
            gr: Gamma Ray curve array
            cnl: Neutron curve array  
            den: Density curve array
            rlld: Deep resistivity curve array
            
        Returns:
            np.ndarray: Predicted Vp values
        """
        # Convert to torch tensors
        curves_dict = {
            'GR': torch.from_numpy(gr).float(),
            'CNL': torch.from_numpy(cnl).float(),
            'DEN': torch.from_numpy(den).float(),
            'RLLD': torch.from_numpy(rlld).float()
        }
        
        # Predict using general interface
        result = self.predict(curves_dict)
        
        # Return as numpy array for backward compatibility
        if isinstance(result, dict):
            return result['VP'].cpu().numpy()
        else:
            return result.cpu().numpy()
    
    def predict_from_file(self, file_path: str) -> np.ndarray:
        """
        Predict Vp from HDF5 file (backward compatible)
        
        Args:
            file_path: Path to HDF5 file with curve data
            
        Returns:
            np.ndarray: Predicted Vp values
        """
        # Use the existing LAS processor
        from ..las_processor import LASProcessor
        processor = LASProcessor()
        
        # Process file to get curves
        curves = processor.process_hdf5_file(file_path)
        
        # Extract required curves
        gr = curves.get('GR', np.zeros(len(curves[list(curves.keys())[0]])))
        cnl = curves.get('CNL', np.zeros(len(curves[list(curves.keys())[0]])))
        den = curves.get('DEN', np.zeros(len(curves[list(curves.keys())[0]])))
        rlld = curves.get('RLLD', np.ones(len(curves[list(curves.keys())[0]])))
        
        return self.predict_from_curves(gr, cnl, den, rlld)
    
    def predict(self, curves_data: Dict[str, torch.Tensor]) -> Union[torch.Tensor, Dict[str, torch.Tensor]]:
        """
        General prediction method (new functionality)
        
        Args:
            curves_data: Dictionary mapping curve names to tensors
            
        Returns:
            Prediction results
        """
        with torch.no_grad():
            # Normalize inputs
            normalized_inputs = self.normalizer.normalize_inputs(curves_data)
            
            # Prepare input tensor [B, C, L] where B=1 for single prediction
            input_list = []
            for curve in ['GR', 'CNL', 'DEN', 'RLLD']:  # Fixed order for compatibility
                if curve in normalized_inputs:
                    input_list.append(normalized_inputs[curve])
                else:
                    # Fill missing curves with zeros
                    seq_len = list(normalized_inputs.values())[0].shape[-1]
                    input_list.append(torch.zeros(seq_len))
            
            # Stack to create input tensor
            input_tensor = torch.stack(input_list, dim=0).unsqueeze(0)  # [1, 4, L]
            input_tensor = input_tensor.to(self.device)
            
            # Forward pass
            predictions = self.model(input_tensor)
            
            # Denormalize predictions
            if isinstance(predictions, dict):
                denormalized = self.normalizer.denormalize_predictions(predictions)
            else:
                # Single tensor output
                denormalized = self.normalizer.denormalize_predictions(predictions, ['VP'])
            
            return denormalized
    
    def get_model_info(self) -> Dict:
        """
        Get model information (backward compatible)
        
        Returns:
            dict: Model information
        """
        info = {
            'model_type': self.model_type,
            'model_path': self.model_path,
            'device': str(self.device),
            'input_curves': ['GR', 'CNL', 'DEN', 'RLLD'],
            'output_curves': ['VP'],
            'vp_range': (40, 400),
            'architecture': 'VpTransformer (GeneralWellLogTransformer backend)'
        }
        
        if hasattr(self.model, 'get_model_info'):
            info.update(self.model.get_model_info())
        
        return info


class VpTransformerAPI:
    """
    Advanced VpTransformer API (backward compatible)
    
    Maintains compatibility with the original advanced API while providing
    enhanced features through the new infrastructure.
    """
    
    def __init__(self, 
                 model_path: str,
                 device: str = 'auto',
                 model_type: str = 'base'):
        """
        Initialize VpTransformerAPI
        
        Args:
            model_path: Path to model checkpoint
            device: Device specification ('auto', 'cpu', 'cuda', etc.)
            model_type: Model size type
        """
        if device == 'auto':
            self.device = get_device()
        else:
            self.device = torch.device(device)
        
        self.model_type = model_type
        self.model_path = model_path
        
        # Initialize predictor
        device_id = 0 if 'cuda' in str(self.device) else -1
        self.predictor = VpPredictor(
            model_path=model_path,
            device_id=device_id,
            model_type=model_type
        )
    
    def predict(self, 
                data: Union[Dict, str],
                format: str = 'curves',
                validate: bool = True) -> Dict:
        """
        Advanced prediction method with validation
        
        Args:
            data: Input data (dict of curves or file path)
            format: Data format ('curves', 'file', 'dataframe')
            validate: Whether to validate inputs
            
        Returns:
            dict: Prediction results with metadata
        """
        if validate:
            # Basic validation
            if format == 'curves' and not isinstance(data, dict):
                raise ValueError("For 'curves' format, data must be a dictionary")
        
        # Perform prediction
        if format == 'file':
            vp_pred = self.predictor.predict_from_file(data)
            predictions = {'VP': vp_pred}
        else:
            # Convert numpy arrays to torch tensors if needed
            if isinstance(data, dict):
                torch_data = {}
                for key, value in data.items():
                    if isinstance(value, np.ndarray):
                        torch_data[key] = torch.from_numpy(value).float()
                    else:
                        torch_data[key] = value
                result = self.predictor.predict(torch_data)
                predictions = result if isinstance(result, dict) else {'VP': result}
            else:
                raise ValueError(f"Unsupported data format: {format}")
        
        # Prepare result
        result = {
            'predictions': predictions,
            'model_info': self.predictor.get_model_info(),
            'validation_passed': validate,
            'format': format
        }
        
        return result
    
    def batch_predict(self, data_list: List) -> List[Dict]:
        """
        Batch prediction for multiple samples
        
        Args:
            data_list: List of data samples
            
        Returns:
            list: List of prediction results
        """
        results = []
        for data in data_list:
            result = self.predict(data)
            results.append(result)
        return results
    
    def verify_model_integrity(self) -> Dict:
        """
        Verify model integrity and compatibility
        
        Returns:
            dict: Integrity check results
        """
        try:
            # Test prediction with dummy data
            dummy_curves = {
                'GR': torch.randn(640),
                'CNL': torch.randn(640),
                'DEN': torch.randn(640),
                'RLLD': torch.randn(640)
            }
            
            result = self.predictor.predict(dummy_curves)
            
            integrity_check = {
                'model_loads': True,
                'prediction_works': True,
                'output_shape_correct': True,
                'device_compatible': True,
                'status': 'PASSED'
            }
            
            # Check output shape
            if isinstance(result, dict):
                vp_shape = result['VP'].shape
            else:
                vp_shape = result.shape
            
            if vp_shape[-1] != 640:  # Check sequence length
                integrity_check['output_shape_correct'] = False
                integrity_check['status'] = 'WARNING'
            
        except Exception as e:
            integrity_check = {
                'model_loads': False,
                'prediction_works': False,
                'output_shape_correct': False,
                'device_compatible': False,
                'status': 'FAILED',
                'error': str(e)
            }
        
        return integrity_check


# Factory functions for backward compatibility
def create_vp_predictor(model_path: str, device_id: int = 0, model_type: str = "base") -> VpPredictor:
    """Create VpPredictor instance (factory function)"""
    return VpPredictor(model_path=model_path, device_id=device_id, model_type=model_type)

def create_vp_api(model_path: str, device: str = 'auto', model_type: str = 'base') -> VpTransformerAPI:
    """Create VpTransformerAPI instance (factory function)"""
    return VpTransformerAPI(model_path=model_path, device=device, model_type=model_type)