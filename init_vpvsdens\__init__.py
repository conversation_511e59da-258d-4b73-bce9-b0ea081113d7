"""
init_vpvsdens - Specialized MWLT for Vp, Vs, and Density Prediction

This package provides a specialized version of the Multi-target Well Log Transformer
optimized for three key petrophysical targets:
- Vp (compressional/sonic AC) 
- Vs (shear/DTS)
- Density (DEN/RHOB)

The architecture maintains the MWLT backbone while providing specialized
configurations, normalization, and training templates for each curve type.
"""

__version__ = "1.0.0"
__author__ = "MWLT Team"

# Import key components for easy access
from .core.configs.curves import (
    get_curve_config,
    get_supported_curves,
    validate_curve_combination
)

from .core.configs.training import (
    get_training_template,
    get_integrated_template,
    get_curve_training_hints
)

from .core.training.losses import (
    GeneralWellLogLoss,
    create_vp_loss,
    create_density_loss,
    create_shear_loss
)

from .api.vs_predictor import VsPredictor, predict_vs
