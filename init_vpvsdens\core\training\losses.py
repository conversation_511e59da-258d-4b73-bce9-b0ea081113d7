"""
Flexible single-target loss functions for General Well Log Transformer

This module provides configurable loss functions that can adapt to different
curve types with appropriate physics constraints and curve-specific formulations.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, Any, Optional, Union, Tuple, List
import logging
import numpy as np

from ..configs.curves import CURVE_CONFIGURATIONS

logger = logging.getLogger(__name__)


class GeneralWellLogLoss(nn.Module):
    """
    Flexible single-target loss function supporting any curve type
    
    This loss function adapts to different curve types by applying appropriate
    physics constraints, normalization awareness, and curve-specific penalties.
    """
    
    def __init__(
        self,
        target_curve: str,
        loss_type: str = 'mse',
        constraint_weight: float = 1.0,
        physics_constraints: bool = True,
        robust_loss_delta: float = 1.0,
        normalize_loss: bool = True,
        curve_specific_weights: Optional[Dict[str, float]] = None
    ):
        """
        Initialize GeneralWellLogLoss
        
        Args:
            target_curve: Name of target curve (e.g., 'AC', 'DEN', 'CNL')
            loss_type: Type of base loss ('mse', 'mae', 'huber', 'smooth_l1')
            constraint_weight: Weight for physics constraint penalty
            physics_constraints: Whether to apply physics constraints
            robust_loss_delta: Delta parameter for robust losses (Huber, Smooth L1)
            normalize_loss: Whether to normalize loss by curve range
            curve_specific_weights: Optional curve-specific loss weights
        """
        super().__init__()
        
        self.target_curve = target_curve
        self.loss_type = loss_type
        self.constraint_weight = constraint_weight
        self.physics_constraints = physics_constraints
        self.robust_loss_delta = robust_loss_delta
        self.normalize_loss = normalize_loss
        
        # Get curve configuration
        if target_curve not in CURVE_CONFIGURATIONS:
            raise ValueError(f"Target curve '{target_curve}' not found in CURVE_CONFIGURATIONS")
        
        self.curve_config = CURVE_CONFIGURATIONS[target_curve]
        self.physics_range = self.curve_config.get('physics_range', (0, 1))
        
        # Set up loss function
        self.base_loss = self._create_base_loss()
        
        # Curve-specific weights
        self.curve_weights = curve_specific_weights or {}
        
        logger.info(f"GeneralWellLogLoss initialized for '{target_curve}' with {loss_type} loss")
        logger.info(f"  Physics range: {self.physics_range}")
        logger.info(f"  Constraint weight: {constraint_weight}")
        
    def _create_base_loss(self) -> nn.Module:
        """Create base loss function"""
        if self.loss_type == 'mse':
            return nn.MSELoss(reduction='mean')
        elif self.loss_type == 'mae':
            return nn.L1Loss(reduction='mean')
        elif self.loss_type == 'huber':
            return nn.HuberLoss(delta=self.robust_loss_delta, reduction='mean')
        elif self.loss_type == 'smooth_l1':
            return nn.SmoothL1Loss(beta=self.robust_loss_delta, reduction='mean')
        else:
            raise ValueError(f"Unsupported loss type: {self.loss_type}")
    
    def forward(
        self, 
        predictions: torch.Tensor, 
        targets: torch.Tensor,
        denormalized_predictions: Optional[torch.Tensor] = None
    ) -> Dict[str, torch.Tensor]:
        """
        Compute loss for single-target predictions
        
        Args:
            predictions: Model predictions [batch_size, 1, seq_len]
            targets: Target values [batch_size, 1, seq_len] 
            denormalized_predictions: Optional denormalized predictions for physics constraints
            
        Returns:
            Dictionary containing loss components
        """
        # Ensure predictions and targets have the same shape
        if predictions.shape != targets.shape:
            raise ValueError(f"Shape mismatch: predictions {predictions.shape} vs targets {targets.shape}")
        
        # Base loss
        base_loss = self.base_loss(predictions, targets)
        
        # Initialize loss components
        loss_components = {
            'base_loss': base_loss,
            'constraint_loss': torch.tensor(0.0, device=predictions.device),
            'total_loss': base_loss
        }
        
        # Apply physics constraints if enabled
        if self.physics_constraints:
            constraint_loss = self._compute_physics_constraints(
                denormalized_predictions if denormalized_predictions is not None else predictions
            )
            loss_components['constraint_loss'] = constraint_loss
            loss_components['total_loss'] = base_loss + self.constraint_weight * constraint_loss
        
        # Apply curve-specific normalization if enabled
        if self.normalize_loss:
            normalization_factor = self._get_normalization_factor()
            loss_components['total_loss'] = loss_components['total_loss'] * normalization_factor
            loss_components['normalization_factor'] = torch.tensor(normalization_factor, device=predictions.device)
        
        # Apply curve-specific weight if available
        curve_weight = self.curve_weights.get(self.target_curve, 1.0)
        if curve_weight != 1.0:
            loss_components['total_loss'] = loss_components['total_loss'] * curve_weight
            loss_components['curve_weight'] = torch.tensor(curve_weight, device=predictions.device)
        
        return loss_components
    
    def _compute_physics_constraints(self, predictions: torch.Tensor) -> torch.Tensor:
        """
        Compute physics constraint penalties for the target curve
        
        Args:
            predictions: Predictions to constrain [batch_size, 1, seq_len]
            
        Returns:
            Constraint penalty tensor
        """
        min_val, max_val = self.physics_range
        
        # Penalty for values below minimum
        below_min = torch.relu(min_val - predictions)
        
        # Penalty for values above maximum  
        above_max = torch.relu(predictions - max_val)
        
        # Total constraint penalty
        constraint_loss = torch.mean(below_min**2 + above_max**2)
        
        return constraint_loss
    
    def _get_normalization_factor(self) -> float:
        """Get normalization factor based on curve range"""
        min_val, max_val = self.physics_range
        curve_range = max_val - min_val
        
        # Normalize by curve range to make losses comparable across different curves
        if curve_range > 0:
            return 1.0 / curve_range
        else:
            return 1.0
    
    def get_loss_info(self) -> Dict[str, Any]:
        """Get information about the loss configuration"""
        return {
            'target_curve': self.target_curve,
            'loss_type': self.loss_type,
            'constraint_weight': self.constraint_weight,
            'physics_constraints': self.physics_constraints,
            'physics_range': self.physics_range,
            'normalize_loss': self.normalize_loss,
            'robust_loss_delta': self.robust_loss_delta
        }


class CurveSpecificLossFactory:
    """
    Factory for creating curve-specific loss functions with optimized parameters
    """
    
    # Optimized loss configurations for different curve types
    CURVE_LOSS_CONFIGS = {
        'AC': {  # Sonic velocity
            'loss_type': 'mse',
            'constraint_weight': 1.0,
            'physics_constraints': True,
            'normalize_loss': True
        },
        'DEN': {  # Bulk density
            'loss_type': 'mse',
            'constraint_weight': 0.5,  # Density is usually well-behaved
            'physics_constraints': True,
            'normalize_loss': True
        },
        'CNL': {  # Neutron porosity
            'loss_type': 'huber',  # Robust to outliers
            'constraint_weight': 0.8,
            'physics_constraints': True,
            'robust_loss_delta': 1.5,
            'normalize_loss': True
        },
        'GR': {  # Gamma ray
            'loss_type': 'mse',
            'constraint_weight': 0.3,  # GR can be noisy
            'physics_constraints': True,
            'normalize_loss': True
        },
        'RLLD': {  # Resistivity
            'loss_type': 'mae',  # Robust to extreme values
            'constraint_weight': 0.2,  # Resistivity has wide dynamic range
            'physics_constraints': True,
            'normalize_loss': True
        },
        'DTS': {  # Shear sonic
            'loss_type': 'huber',  # Robust to outliers in shear measurements
            'constraint_weight': 0.8,
            'physics_constraints': True,
            'robust_loss_delta': 2.0,
            'normalize_loss': True
        }
    }
    
    @classmethod
    def create_loss(
        self,
        target_curve: str,
        custom_config: Optional[Dict[str, Any]] = None
    ) -> GeneralWellLogLoss:
        """
        Create optimized loss function for specific curve type
        
        Args:
            target_curve: Target curve name
            custom_config: Optional custom configuration overrides
            
        Returns:
            Configured GeneralWellLogLoss instance
        """
        # Get default configuration for curve
        default_config = self.CURVE_LOSS_CONFIGS.get(target_curve, {
            'loss_type': 'mse',
            'constraint_weight': 1.0,
            'physics_constraints': True,
            'normalize_loss': True
        })
        
        # Apply custom overrides
        if custom_config:
            config = {**default_config, **custom_config}
        else:
            config = default_config
        
        return GeneralWellLogLoss(target_curve=target_curve, **config)
    
    @classmethod
    def get_recommended_config(cls, target_curve: str) -> Dict[str, Any]:
        """Get recommended configuration for a curve type"""
        return cls.CURVE_LOSS_CONFIGS.get(target_curve, {})


class VpLossCompatible(GeneralWellLogLoss):
    """
    Backward-compatible wrapper for VpLoss using GeneralWellLogLoss
    
    This provides exact API compatibility with the original VpLoss while
    using the new flexible implementation underneath.
    """
    
    def __init__(self, constraint_weight: float = 1.0, device: Optional[torch.device] = None):
        """
        Initialize VpLoss-compatible wrapper
        
        Args:
            constraint_weight: Weight for physics constraint penalty
            device: Target device
        """
        # Initialize with AC (Vp) specific configuration
        super().__init__(
            target_curve='AC',
            loss_type='mse',
            constraint_weight=constraint_weight,
            physics_constraints=True,
            normalize_loss=False  # Original VpLoss didn't normalize
        )
        
        if device is not None:
            self.to(device)
    
    def forward(
        self, 
        pred_vp: torch.Tensor, 
        target_vp: torch.Tensor
    ) -> torch.Tensor:
        """
        Forward pass compatible with original VpLoss
        
        Args:
            pred_vp: Predicted Vp values
            target_vp: Target Vp values
            
        Returns:
            Total loss value (scalar tensor)
        """
        loss_components = super().forward(pred_vp, target_vp)
        return loss_components['total_loss']


class MultiCurveLossBalancer:
    """
    Utility for balancing losses across multiple single-target models
    
    Note: This is prepared for future multi-target support while maintaining
    single-target focus for Phase 2.
    """
    
    def __init__(self, target_curves: List[str]):
        """Initialize loss balancer"""
        self.target_curves = target_curves
        self.loss_functions = {}
        
        # Create loss function for each curve
        for curve in target_curves:
            self.loss_functions[curve] = CurveSpecificLossFactory.create_loss(curve)
    
    def compute_balanced_loss(
        self,
        predictions: Dict[str, torch.Tensor],
        targets: Dict[str, torch.Tensor],
        loss_weights: Optional[Dict[str, float]] = None
    ) -> Dict[str, torch.Tensor]:
        """
        Compute balanced loss across multiple curves
        
        Note: Prepared for future use, not needed for Phase 2 single-target implementation
        """
        total_loss = 0.0
        loss_components = {}
        
        weights = loss_weights or {curve: 1.0 for curve in self.target_curves}
        
        for curve in self.target_curves:
            if curve in predictions and curve in targets:
                curve_loss_components = self.loss_functions[curve](
                    predictions[curve], targets[curve]
                )
                
                weighted_loss = curve_loss_components['total_loss'] * weights.get(curve, 1.0)
                total_loss += weighted_loss
                
                loss_components[f'{curve}_loss'] = weighted_loss
                loss_components[f'{curve}_base'] = curve_loss_components['base_loss']
                loss_components[f'{curve}_constraint'] = curve_loss_components['constraint_loss']
        
        loss_components['total_loss'] = total_loss
        return loss_components


# Convenience functions for creating common loss configurations
def create_vp_loss(constraint_weight: float = 1.0) -> GeneralWellLogLoss:
    """Create Vp (AC) prediction loss"""
    return CurveSpecificLossFactory.create_loss('AC', {
        'constraint_weight': constraint_weight
    })


def create_density_loss(constraint_weight: float = 0.5) -> GeneralWellLogLoss:
    """Create density prediction loss"""
    return CurveSpecificLossFactory.create_loss('DEN', {
        'constraint_weight': constraint_weight
    })


def create_neutron_loss(robust: bool = True) -> GeneralWellLogLoss:
    """Create neutron porosity prediction loss"""
    config = {'constraint_weight': 0.8}
    if robust:
        config.update({
            'loss_type': 'huber',
            'robust_loss_delta': 1.5
        })
    
    return CurveSpecificLossFactory.create_loss('CNL', config)


def create_gamma_ray_loss(constraint_weight: float = 0.3) -> GeneralWellLogLoss:
    """Create gamma ray prediction loss"""
    return CurveSpecificLossFactory.create_loss('GR', {
        'constraint_weight': constraint_weight
    })


def create_resistivity_loss(robust: bool = True) -> GeneralWellLogLoss:
    """Create resistivity prediction loss"""
    config = {'constraint_weight': 0.2}
    if robust:
        config.update({
            'loss_type': 'mae'  # MAE is robust to extreme values
        })
    
    return CurveSpecificLossFactory.create_loss('RLLD', config)


def create_shear_loss(constraint_weight: float = 0.8, robust: bool = True) -> GeneralWellLogLoss:
    """Create shear sonic (DTS/Vs) prediction loss"""
    config = {'constraint_weight': constraint_weight}
    if robust:
        config.update({
            'loss_type': 'huber',
            'robust_loss_delta': 2.0
        })
    
    return CurveSpecificLossFactory.create_loss('DTS', config)
