"""
Setup script for the standalone VP Prediction module
"""

from setuptools import setup, find_packages
import os

# Read the README file
def read_readme():
    readme_path = os.path.join(os.path.dirname(__file__), 'README.md')
    if os.path.exists(readme_path):
        with open(readme_path, 'r', encoding='utf-8') as f:
            return f.read()
    return "Standalone VP Prediction Module"

# Read requirements
def read_requirements():
    req_path = os.path.join(os.path.dirname(__file__), 'requirements.txt')
    if os.path.exists(req_path):
        with open(req_path, 'r', encoding='utf-8') as f:
            return [line.strip() for line in f if line.strip() and not line.startswith('#')]
    return [
        'torch>=1.8.0',
        'numpy>=1.19.0',
        'h5py>=3.1.0',
        'scikit-learn>=0.24.0',
        'matplotlib>=3.3.0',
        'pandas>=1.2.0',
        'tqdm>=4.50.0'
    ]

setup(
    name="init_vp_pred",
    version="1.0.0",
    description="Standalone VP (Velocity Prediction) Module using Transformer Models",
    long_description=read_readme(),
    long_description_content_type="text/markdown",
    author="MWLT System",
    author_email="",
    url="",
    packages=find_packages(),
    include_package_data=True,
    package_data={
        'init_vp_pred': [
            '*.las',
            '*.hdf5',
            '*.md',
            'vp_predictor/**/*',
        ]
    },
    install_requires=read_requirements(),
    python_requires=">=3.7",
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Science/Research",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.7",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
    ],
    entry_points={
        'console_scripts': [
            'vp-train=init_vp_pred.train_vp_improved:main',
            'vp-test=init_vp_pred.test_vp_pipeline:run_comprehensive_test',
        ],
    },
    keywords="velocity prediction, transformer, well log, geophysics, machine learning",
)
